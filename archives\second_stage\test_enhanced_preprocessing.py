#!/usr/bin/env python3
"""
Test script for enhanced preprocessing pipeline.
Validates the integration of cp_preconditioning techniques with deep learning workflow.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from enhanced_preprocessing import enhanced_preprocessing_pipeline, EnhancedLogPreprocessor
import warnings
warnings.filterwarnings('ignore')

def create_synthetic_well_data(n_wells=3, n_samples_per_well=1000, n_features=4):
    """Create synthetic well log data for testing."""
    np.random.seed(42)
    
    all_data = []
    well_names = [f'WELL_{i+1:02d}' for i in range(n_wells)]
    feature_names = ['GR', 'NPHI', 'RHOB', 'RT']
    
    for well in well_names:
        # Create realistic well log patterns
        depth = np.linspace(1000, 1000 + n_samples_per_well, n_samples_per_well)
        
        # GR (Gamma Ray): 0-300 API units
        gr = 50 + 30 * np.sin(depth/100) + 20 * np.random.normal(0, 1, n_samples_per_well)
        gr = np.clip(gr, 0, 300)
        
        # NPHI (Neutron Porosity): 0-0.5 fraction
        nphi = 0.15 + 0.1 * np.sin(depth/150) + 0.05 * np.random.normal(0, 1, n_samples_per_well)
        nphi = np.clip(nphi, 0, 0.5)
        
        # RHOB (Bulk Density): 1.5-3.0 g/cc
        rhob = 2.3 + 0.3 * np.cos(depth/200) + 0.1 * np.random.normal(0, 1, n_samples_per_well)
        rhob = np.clip(rhob, 1.5, 3.0)
        
        # RT (Resistivity): 0.1-1000 ohm-m (log scale)
        rt = np.exp(2 + 2 * np.sin(depth/300) + 0.5 * np.random.normal(0, 1, n_samples_per_well))
        rt = np.clip(rt, 0.1, 1000)
        
        # Add some outliers (5% of data)
        outlier_indices = np.random.choice(n_samples_per_well, size=int(0.05 * n_samples_per_well), replace=False)
        gr[outlier_indices] = np.random.uniform(400, 500, len(outlier_indices))  # Extreme GR values
        nphi[outlier_indices] = np.random.uniform(0.8, 1.2, len(outlier_indices))  # Extreme NPHI values
        
        # Add some missing sections (realistic for well logs)
        missing_sections = [
            (200, 250),  # Tool failure section
            (600, 620),  # Bad hole conditions
            (850, 870)   # Casing/cement section
        ]
        
        for start, end in missing_sections:
            if start < n_samples_per_well and end < n_samples_per_well:
                gr[start:end] = np.nan
                nphi[start:end] = np.nan
                rhob[start:end] = np.nan
                rt[start:end] = np.nan
        
        # Create DataFrame for this well
        well_df = pd.DataFrame({
            'MD': depth,
            'WELL': well,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'RT': rt
        })
        
        all_data.append(well_df)
    
    # Combine all wells
    combined_df = pd.concat(all_data, ignore_index=True)
    
    print(f"Created synthetic data:")
    print(f"  • {n_wells} wells")
    print(f"  • {n_samples_per_well} samples per well")
    print(f"  • {n_features} features: {feature_names}")
    print(f"  • Total data points: {len(combined_df)}")
    print(f"  • Missing data rate: {combined_df[feature_names].isnull().sum().sum() / (len(combined_df) * n_features):.1%}")
    
    return combined_df, feature_names

def test_enhanced_preprocessing():
    """Test the enhanced preprocessing pipeline."""
    print("=" * 60)
    print(" TESTING ENHANCED PREPROCESSING PIPELINE")
    print("=" * 60)
    
    # Create test data
    df, feature_names = create_synthetic_well_data()
    target_col = 'RT'  # Use resistivity as target
    feature_cols = [col for col in feature_names if col != target_col]
    
    print(f"\nTest configuration:")
    print(f"  • Feature columns: {feature_cols}")
    print(f"  • Target column: {target_col}")
    
    # Test enhanced preprocessing pipeline
    try:
        sequences, sequences_missing, scalers, report = enhanced_preprocessing_pipeline(
            df=df,
            feature_cols=feature_cols,
            target_col=target_col,
            sequence_len=64,
            sequence_stride=32,
            missing_rate=0.2,
            normalization_method='standard',
            winsorize_percentiles=(0.01, 0.99),
            random_seed=42
        )
        
        print("\n✅ Enhanced preprocessing completed successfully!")
        print(f"   • Clean sequences shape: {sequences.shape}")
        print(f"   • Missing sequences shape: {sequences_missing.shape}")
        print(f"   • Scalers available: {list(scalers.keys())}")
        
        return True, sequences, sequences_missing, scalers, report
        
    except Exception as e:
        print(f"\n❌ Enhanced preprocessing failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None, None

def test_individual_components():
    """Test individual preprocessing components."""
    print("\n" + "=" * 60)
    print(" TESTING INDIVIDUAL COMPONENTS")
    print("=" * 60)
    
    # Create test data
    df, feature_names = create_synthetic_well_data(n_wells=2, n_samples_per_well=500)
    
    # Initialize preprocessor
    preprocessor = EnhancedLogPreprocessor(
        sequence_len=32,
        sequence_stride=16,
        missing_rate=0.15
    )
    
    # Test 1: Winsorization
    print("\n🎯 Testing winsorization...")
    df_winsorized = preprocessor.winsorize_outliers(df, feature_names)
    
    # Test 2: Enhanced normalization
    print("\n📊 Testing enhanced normalization...")
    df_normalized, scalers = preprocessor.normalize_data_enhanced(df_winsorized, feature_names)
    
    # Test 3: Sequence creation with valid intervals
    print("\n🔗 Testing sequence creation...")
    sequences, metadata = preprocessor.create_sequences_enhanced(df_normalized, 'WELL', feature_names)
    
    # Test 4: Realistic missing value introduction
    print("\n❓ Testing missing value introduction...")
    sequences_missing = preprocessor.introduce_realistic_missingness(sequences)
    
    # Test 5: Quality validation
    print("\n✅ Testing quality validation...")
    quality_metrics = preprocessor.validate_preprocessing_quality(sequences, sequences_missing)
    
    print(f"\nComponent test results:")
    print(f"  • Sequences created: {len(sequences)}")
    print(f"  • Metadata entries: {len(metadata)}")
    print(f"  • Quality metrics: {len(quality_metrics)} metrics")
    
    return True

def visualize_preprocessing_results(sequences, sequences_missing):
    """Create visualizations to show preprocessing results."""
    print("\n📊 Creating preprocessing visualizations...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Enhanced Preprocessing Results', fontsize=16)
    
    # Plot 1: Original vs Missing Data Distribution
    axes[0, 0].hist(sequences[~np.isnan(sequences)], bins=50, alpha=0.7, label='Original', density=True)
    axes[0, 0].hist(sequences_missing[~np.isnan(sequences_missing)], bins=50, alpha=0.7, label='With Missing', density=True)
    axes[0, 0].set_title('Data Distribution Comparison')
    axes[0, 0].set_xlabel('Normalized Values')
    axes[0, 0].set_ylabel('Density')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Missing Data Pattern
    missing_mask = np.isnan(sequences_missing)
    missing_rate_per_sequence = missing_mask.mean(axis=(1, 2))
    axes[0, 1].hist(missing_rate_per_sequence, bins=30, alpha=0.7, color='red')
    axes[0, 1].set_title('Missing Data Rate Distribution')
    axes[0, 1].set_xlabel('Missing Rate per Sequence')
    axes[0, 1].set_ylabel('Count')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Sample Sequence Comparison
    seq_idx = 0
    axes[1, 0].plot(sequences[seq_idx, :, 0], label='Original', linewidth=2)
    axes[1, 0].plot(sequences_missing[seq_idx, :, 0], label='With Missing', linewidth=2, alpha=0.7)
    axes[1, 0].set_title(f'Sample Sequence {seq_idx} (Feature 0)')
    axes[1, 0].set_xlabel('Sequence Position')
    axes[1, 0].set_ylabel('Normalized Value')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Data Quality Metrics
    original_nan_rate = np.isnan(sequences).mean()
    missing_nan_rate = np.isnan(sequences_missing).mean()
    
    metrics = ['Original NaN Rate', 'Missing NaN Rate']
    values = [original_nan_rate, missing_nan_rate]
    colors = ['blue', 'red']
    
    bars = axes[1, 1].bar(metrics, values, color=colors, alpha=0.7)
    axes[1, 1].set_title('Data Quality Metrics')
    axes[1, 1].set_ylabel('NaN Rate')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.1%}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('enhanced_preprocessing_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Visualizations saved as 'enhanced_preprocessing_results.png'")

def main():
    """Run all tests."""
    print("🚀 Starting Enhanced Preprocessing Tests...")
    
    # Test 1: Full pipeline
    success, sequences, sequences_missing, scalers, report = test_enhanced_preprocessing()
    
    if not success:
        print("❌ Pipeline test failed. Exiting.")
        return
    
    # Test 2: Individual components
    test_individual_components()
    
    # Test 3: Visualizations
    if sequences is not None and sequences_missing is not None:
        visualize_preprocessing_results(sequences, sequences_missing)
    
    print("\n" + "=" * 60)
    print(" ALL TESTS COMPLETED SUCCESSFULLY! ✅")
    print("=" * 60)
    print("\nEnhanced preprocessing is ready for integration with deep learning models.")
    print("Key improvements:")
    print("  • Statistical outlier removal (winsorization)")
    print("  • Intelligent sequence creation with valid intervals")
    print("  • Realistic missing data patterns")
    print("  • Enhanced normalization with better NaN handling")
    print("  • Comprehensive quality validation")

if __name__ == "__main__":
    main()
