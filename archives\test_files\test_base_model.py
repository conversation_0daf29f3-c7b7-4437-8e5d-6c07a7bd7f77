#!/usr/bin/env python3
"""
Test script for BaseAdvancedModel implementation.
"""

import torch
import numpy as np
from models.advanced_models.base_model import BaseAdvancedModel, validate_model_compatibility

class MockAdvancedModel(BaseAdvancedModel):
    """Mock implementation for testing BaseAdvancedModel."""
    
    def _initialize_model(self):
        """Initialize a mock model."""
        self.model = MockPyPOTSModel()
        print("🔧 Mock model initialized")
    
    def _prepare_data(self, data, truth_data=None):
        """Prepare data using the base PyPOTS method."""
        return self._prepare_pypots_data(data, truth_data)

class MockPyPOTSModel:
    """Mock PyPOTS model for testing."""
    
    def fit(self, dataset):
        """Mock training."""
        print("🚀 Mock model training...")
        # Simulate training
        pass
    
    def predict(self, dataset):
        """Mock prediction."""
        print("🔮 Mock model prediction...")
        # Return the input data with NaNs filled with zeros
        X = dataset['X']
        result = np.nan_to_num(X, nan=0.0)
        return result

def test_base_model():
    """Test BaseAdvancedModel functionality."""
    print("🧪 Testing BaseAdvancedModel implementation...")
    print("=" * 60)
    
    # Create test data
    np.random.seed(42)
    torch.manual_seed(42)
    
    batch_size = 5
    sequence_len = 32
    n_features = 4
    
    # Create test data with missing values
    test_data = torch.randn(batch_size, sequence_len, n_features)
    test_data[test_data > 0.5] = float('nan')
    
    # Create ground truth data
    truth_data = torch.randn(batch_size, sequence_len, n_features)
    
    print(f"Test data shape: {test_data.shape}")
    print(f"Missing values: {torch.isnan(test_data).sum().item()}")
    
    # Test model initialization
    print("\n🔧 Testing model initialization...")
    model = MockAdvancedModel(
        n_features=n_features,
        sequence_len=sequence_len,
        epochs=2,
        batch_size=batch_size
    )
    
    # Test model info
    print("\n📋 Testing model info...")
    info = model.get_model_info()
    print(f"Model info: {info}")
    
    # Test data validation
    print("\n🔍 Testing data validation...")
    is_valid = model._validate_input_data(test_data)
    print(f"Data validation result: {is_valid}")
    
    # Test PyPOTS data preparation
    print("\n📊 Testing PyPOTS data preparation...")
    pypots_data = model._prepare_pypots_data(test_data, truth_data)
    print(f"PyPOTS data keys: {list(pypots_data.keys())}")
    print(f"X shape: {pypots_data['X'].shape}")
    print(f"Indicating mask shape: {pypots_data['indicating_mask'].shape}")
    
    # Test training
    print("\n🚀 Testing model training...")
    try:
        model.fit(test_data, truth_data, epochs=1, batch_size=batch_size)
        print("✅ Training completed successfully")
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return False
    
    # Test prediction
    print("\n🔮 Testing model prediction...")
    try:
        predictions = model.predict(test_data)
        print(f"Predictions shape: {predictions.shape}")
        print(f"Predictions type: {type(predictions)}")
        print("✅ Prediction completed successfully")
    except Exception as e:
        print(f"❌ Prediction failed: {e}")
        return False
    
    # Test evaluation
    print("\n📊 Testing model evaluation...")
    try:
        metrics = model.evaluate_imputation(truth_data, predictions)
        print(f"Evaluation metrics: {metrics}")
        print("✅ Evaluation completed successfully")
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        return False
    
    # Test model compatibility validation
    print("\n🔍 Testing model compatibility validation...")
    is_compatible = validate_model_compatibility(MockAdvancedModel)
    print(f"Model compatibility: {is_compatible}")
    
    print("\n" + "=" * 60)
    print("🎉 All BaseAdvancedModel tests passed!")
    return True

def test_data_preparation_utils():
    """Test data preparation utilities."""
    print("\n🧪 Testing data preparation utilities...")
    
    try:
        from models.advanced_models.utils.data_preparation import (
            prepare_pypots_dataset, validate_pypots_dataset, add_artificial_missingness
        )
        
        # Create test data
        data = np.random.randn(3, 20, 4)
        
        # Test PyPOTS dataset preparation
        dataset = prepare_pypots_dataset(data)
        is_valid = validate_pypots_dataset(dataset)
        print(f"Dataset validation: {is_valid}")
        
        # Test artificial missingness
        data_missing, missing_mask = add_artificial_missingness(data, missing_rate=0.3)
        print(f"Artificial missingness added successfully")
        
        print("✅ Data preparation utilities test passed")
        return True
        
    except Exception as e:
        print(f"❌ Data preparation utilities test failed: {e}")
        return False

if __name__ == "__main__":
    success1 = test_base_model()
    success2 = test_data_preparation_utils()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! BaseAdvancedModel implementation is ready.")
    else:
        print("\n❌ Some tests failed. Review implementation.")
