# The core issue is in how the data is prepared for the final prediction (imputation) step.

1.  **Incorrect Data for Prediction:** In `impute_logs_deep`, during the "Prediction phase (full dataset)", you call `create_sequences` on the entire dataset:
    ```python
    # ml_core.py -> impute_logs_deep
    # ...
    # 5. Imputation (Prediction on all data)
    print("Prediction phase (full dataset)...")
    
    # Prepare the full dataset for prediction
    df_scaled, full_scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing)
    
    # We need the original data with NaNs to feed into the model
    sequences_to_predict, metadata = create_sequences(df_scaled, 'WELL', all_features, ...)
    # ...
    imputed_full_tensor = model.predict(prediction_tensor)
    ```

2.  **How `create_sequences` Works:** Your `create_sequences` function (in `data_handler.py`) is designed to find **continuous, non-NaN intervals** of data to create valid sequences for training. When you pass `df_scaled` (which contains the original data, including the non-missing target values) to it, it correctly identifies the parts of the log that have data and creates sequences from them.

3.  **The Leak:** The `sequences_to_predict` variable now contains sequences where the target log is **already filled with the correct values**. When you feed this to `model.predict()`, the model sees the answer and simply outputs it. The result is a "perfect" prediction because no actual imputation occurred.

### A Secondary Bug in Re-assembly

There is another subtle but critical bug in the re-assembly logic that completes the data leakage loop.

```python
# ml_core.py -> impute_logs_deep (Original Buggy Version)
# ...
# 6. Post-processing & Re-assembly
# ...
imputed_df_scaled = df_scaled.copy()
update_mask = pred_count_df[all_features] > 0
# This line replaces the original data with the perfectly "predicted" data
imputed_df_scaled[all_features] = imputed_df_scaled[all_features].where(~update_mask, avg_pred_df_scaled[all_features])

# ... unscaling ...

# Final assignment to result dataframe
res_df = df.copy()
# ...
# This line assigns the perfectly reconstructed target log to the prediction column
res_df[pred_col] = imputed_df_unscaled[target_col] 
# This line then fills NaNs from a column that has no NaNs where original data existed
res_df[imp_col] = res_df[target_col].fillna(res_df[pred_col]) 
```
This logic ensures that the leaked, perfect predictions are written back into the final result dataframe, leading to the R²=1 and MAE=0 scores.

---

### The Solution: Correcting the Prediction & Re-assembly Workflow

To fix this, we need to ensure the model receives sequences with the target values missing (as `NaN`) and then correctly re-assemble the results, filling *only* the missing values.

Here is the corrected `impute_logs_deep` function for `ml_core.py`.

#### Corrected `impute_logs_deep` Function

This version modifies the "Prediction" and "Re-assembly" phases to prevent data leakage and correctly integrate the imputed values.

```python
# In file: ml_core.py

def impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing=True):
    """Main imputation routine for a single deep learning model."""
    print(f"--- Running Deep Learning Model: {model_config['name']} ---")

    # 1. Data Preparation
    all_features = feature_cols + [target_col]
    
    # --- TRAIN/VALIDATION SPLIT ---
    wells = df['WELL'].unique()
    train_wells, val_wells = train_test_split(wells, test_size=0.2, random_state=42)
    train_df = df[df['WELL'].isin(train_wells)]
    val_df = df[df['WELL'].isin(val_wells)]
    print(f"Training wells: {len(train_wells)}, Validation wells: {len(val_wells)}")

    # Normalize training data and get scalers
    train_df_scaled, scalers = normalize_data(train_df, all_features, use_enhanced=use_enhanced_preprocessing)

    # Create sequences for training
    train_sequences_true, train_metadata = create_sequences(train_df_scaled, 'WELL', all_features,
                                               sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    
    if train_sequences_true.shape[0] == 0:
        print("❌ ERROR: No training sequences could be created. This might be due to insufficient continuous data.")
        return df, None
        
    train_sequences_missing = introduce_missingness(train_sequences_true, target_col_name=target_col,
                                                    feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)

    # Create sequences for validation
    val_df_scaled, _ = normalize_data(val_df, all_features, use_enhanced=use_enhanced_preprocessing, scalers=scalers)
    val_sequences_true, val_metadata = create_sequences(val_df_scaled, 'WELL', all_features,
                                             sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    val_sequences_missing = introduce_missingness(val_sequences_true, target_col_name=target_col,
                                                  feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)

    # Convert to PyTorch tensors
    def to_tensor(data, name):
        if not isinstance(data, np.ndarray): data = np.array(data, dtype=np.float32)
        if data.dtype != np.float32: data = data.astype(np.float32)
        return torch.from_numpy(data)

    train_tensor = to_tensor(train_sequences_missing, "train_sequences_missing")
    truth_tensor = to_tensor(train_sequences_true, "train_sequences_true")
    val_train_tensor = to_tensor(val_sequences_missing, "val_sequences_missing")
    val_truth_tensor = to_tensor(val_sequences_true, "val_sequences_true")

    # 2. Model Initialization
    hparams['n_features'] = len(all_features)
    if not DEEP_MODELS_AVAILABLE or model_config['model_class'] is None:
        print(f"Deep learning model {model_config['name']} not available. Skipping.")
        return df, None
    model = model_config['model_class'](**hparams)

    # 3. Training
    model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'], batch_size=hparams['batch_size'])

    # 4. Evaluation on Validation Set
    print("Evaluation phase...")
    mae, r2, rmse, composite_score = -1, -1, -1, -1
    if val_train_tensor.shape[0] > 0:
        imputed_val_tensor = model.predict(val_train_tensor)
        target_idx = all_features.index(target_col)
        
        # Only evaluate on the initially missing values for a true test
        val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
        y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
        y_true_val = val_truth_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()

        if len(y_true_val) > 0:
            mae = mean_absolute_error(y_true_val, y_pred_val)
            r2 = r2_score(y_true_val, y_pred_val)
            rmse = np.sqrt(mean_squared_error(y_true_val, y_pred_val))
            r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
            composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
            print(f"Validation Metrics: MAE={mae:.4f}, R2={r2:.4f}, RMSE={rmse:.4f}, Composite={composite_score:.4f}")
        else:
            print("Warning: No missing values in validation set to evaluate.")
    else:
        print("Warning: No validation sequences to evaluate.")

    # 5. Imputation (Prediction on all data) - **BUG FIX AREA**
    print("Prediction phase (full dataset)...")
    
    # Prepare the full dataset for prediction
    df_scaled, full_scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing, scalers=scalers)
    
    # BUG FIX: To create sequences for prediction, we must have continuous *features*.
    # We forward-fill and back-fill the features, but leave the target NaNs intact.
    prediction_input_df = df_scaled.copy()
    prediction_input_df[feature_cols] = prediction_input_df[feature_cols].ffill().bfill()
    
    # Now, create sequences from this feature-filled dataframe.
    # The sequences will contain NaNs in the target column, which is what the model must predict.
    sequences_to_predict, metadata = create_sequences(prediction_input_df, 'WELL', all_features,
                                                      sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    if sequences_to_predict.shape[0] == 0:
        print("❌ ERROR: No sequences created from the full dataset for prediction.")
        return df, None

    # Predict on the sequences (which now correctly contain NaNs)
    prediction_tensor = torch.from_numpy(sequences_to_predict.astype(np.float32))
    imputed_full_tensor = model.predict(prediction_tensor)
    imputed_sequences = imputed_full_tensor.detach().cpu().numpy()

    # 6. Post-processing & Re-assembly - **BUG FIX AREA**
    print("Post-processing and re-assembling results...")
    
    # Create a new dataframe to hold the imputed values to avoid confusion
    imputed_df_unscaled = df.copy()
    
    # Create placeholder for averaging predictions on overlapping sequences
    pred_sum_df = pd.DataFrame(0.0, index=df.index, columns=all_features)
    pred_count_df = pd.DataFrame(0, index=df.index, columns=all_features)

    for i, seq_meta in enumerate(metadata):
        original_indices = seq_meta['original_indices']
        # This is the predicted sequence, in SCALED format
        predicted_sequence_scaled = imputed_sequences[i]
        
        pred_sum_df.loc[original_indices, all_features] += predicted_sequence_scaled
        pred_count_df.loc[original_indices, all_features] += 1

    # Calculate the average prediction and avoid division by zero
    avg_pred_df_scaled = pred_sum_df.divide(pred_count_df)

    # Inverse transform the predictions to get back to the original scale
    imputed_df_scaled = avg_pred_df_scaled.copy()
    for col in all_features:
        if col in full_scalers and full_scalers[col] is not None:
            valid_mask = imputed_df_scaled[col].notna()
            if valid_mask.any():
                col_data = imputed_df_scaled.loc[valid_mask, [col]].values
                imputed_df_scaled.loc[valid_mask, col] = full_scalers[col].inverse_transform(col_data)

    # Final assignment to result dataframe
    res_df = df.copy()
    pred_col = f'{target_col}_pred'
    imp_col = f'{target_col}_imputed'
    err_col = f'{target_col}_error'
    
    # The '_pred' column shows the model's output for ALL points
    res_df[pred_col] = imputed_df_scaled[target_col]
    
    # The '_imputed' column shows the original data, with NaNs filled by the prediction
    res_df[imp_col] = res_df[target_col].fillna(res_df[pred_col])
    
    # Calculate error only where original data existed to compare against
    mask_orig = res_df[target_col].notna() & res_df[pred_col].notna()
    res_df.loc[mask_orig, err_col] = np.abs(res_df.loc[mask_orig, target_col] - res_df.loc[mask_orig, pred_col])

    print(f"✅ {model_config['name']} imputation complete.")

    eval_results = {
        'mae': mae, 'r2': r2, 'rmse': rmse,
        'model_name': model_config['name'], 'composite_score': composite_score
    }

    return res_df, {
        'target': target_col, 'evaluations': [eval_results],
        'best_model_name': model_config['name'], 'trained_models': {model_config['name']: model}
    }
```

### Summary of Changes:

1.  **Correct Prediction Input:** Before creating prediction sequences, the *feature columns* are filled using `ffill().bfill()`. This ensures `create_sequences` can form continuous sequences without filtering out the rows where the target is missing. The `target_col` is left with its `NaN`s, which is what the model needs to see.
2.  **Robust Re-assembly:** The re-assembly logic is clarified to avoid confusion. It correctly averages predictions from overlapping windows, inverse-transforms them, and then uses these values to create the `_pred` (full model output) and `_imputed` (original + filled NaNs) columns.
3.  **Correct Validation:** The validation logic is updated to only calculate metrics on the values that were *artificially made missing* in the validation set. This provides a more accurate measure of imputation performance.

To apply this fix, **replace the entire `impute_logs_deep` function in your `ml_core.py` file** with the corrected version above. This will resolve the data leakage and you should now see realistic performance metrics for your SAITS and BRITS models.