# Archive - Second Stage Development Files

This directory contains files that were moved from the main pipeline directory to keep the main codebase clean and focused on the core ML log prediction functionality.

## Contents

### Documentation Files
- `ENHANCED_PREPROCESSING_GUIDE.md` - Guide for enhanced preprocessing techniques
- `Guideline_restructuring.md` - Original restructuring guidelines
- `NEXT_STEPS_RESTRUCTURING.md` - Next steps documentation
- `RESTRUCTURING_COMPLETE.md` - Completion status documentation
- `Restructuring_with_deepnet.md` - Deep learning restructuring notes
- `UNET_FIXES_SUMMARY.md` - U-Net fixes summary

### Test Files
- `test_autoencoder_fixes.py` - Autoencoder testing script
- `test_autoencoder_real_data.py` - Real data autoencoder tests
- `test_enhanced_preprocessing.py` - Enhanced preprocessing tests
- `test_enhanced_simple.py` - Simple enhanced tests
- `test_unet_fixes.py` - U-Net fixes testing

### Legacy/Experimental Directories
- `cp_model/` - Original model implementations (preserved for reference)
  - `autoencoder.py`, `neuralnet.py`, `unet.py`, `shallow.py`, `metrics.py`
- `cp_preconditioning/` - Original preprocessing implementations
  - `README.md`, `args.py`, `preprocessing.py`
- `main/` - Empty directory from previous structure

### Experimental Scripts
- `enhanced_preprocessing.py` - Enhanced preprocessing implementation
- `data.py` - Alternative data handling implementation

### Generated Files
- `autoencoder_test_results.png` - Test result visualizations
- `enhanced_preprocessing_results.png` - Preprocessing result plots
- `catboost_info/` - CatBoost training logs and metadata
- `__pycache__/` - Python bytecode cache files

## Purpose

These files were moved to maintain a clean main directory structure focused on the core ML log prediction pipeline. The main pipeline now consists of:

**Core Pipeline Files (Remaining in Root):**
- `main.py` - Main entry point
- `data_handler.py` - Data loading and preprocessing
- `config_handler.py` - Configuration and user input
- `ml_core.py` - ML models and training logic
- `reporting.py` - Visualization and reporting
- `requirements.txt` - Dependencies
- `models/` - Current model implementations
- `utils/` - Utility functions
- `Las/` - Test data directory

## Usage

Files in this archive can be referenced for:
- Understanding the development history
- Accessing alternative implementations
- Running specific tests or experiments
- Reviewing documentation from earlier development stages

To use any of these files, they can be copied back to the main directory or referenced from their current location.
