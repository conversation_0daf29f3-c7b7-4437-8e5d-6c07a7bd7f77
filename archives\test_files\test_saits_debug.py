#!/usr/bin/env python3
"""
Debug script to test SAITS model with minimal example
"""

import numpy as np
import torch
import pandas as pd
from ml_core import MODEL_REGISTRY

def test_saits_model():
    """Test SAITS model with minimal data"""
    print("🔍 Testing SAITS model with minimal data...")
    
    # Get SAITS model from registry
    saits_config = MODEL_REGISTRY['saits']
    print(f"SAITS config: {saits_config}")
    
    # Create minimal test data
    n_samples = 100
    sequence_len = 64
    n_features = 4
    
    # Create synthetic data
    np.random.seed(42)
    data = np.random.randn(n_samples, sequence_len, n_features).astype(np.float32)
    
    # Introduce some missing values
    missing_mask = np.random.random((n_samples, sequence_len, n_features)) < 0.3
    data_with_missing = data.copy()
    data_with_missing[missing_mask] = np.nan
    
    print(f"Data shape: {data.shape}")
    print(f"Data type: {type(data)}")
    print(f"Data dtype: {data.dtype}")
    print(f"Missing values: {np.isnan(data_with_missing).sum()}")
    
    # Convert to tensors
    train_tensor = torch.from_numpy(data_with_missing)
    truth_tensor = torch.from_numpy(data)
    
    print(f"Train tensor shape: {train_tensor.shape}")
    print(f"Train tensor type: {type(train_tensor)}")
    print(f"Train tensor dtype: {train_tensor.dtype}")
    
    # Initialize model
    hparams = {
        'n_features': n_features,
        'sequence_len': sequence_len,
        'epochs': 5,  # Small number for testing
        'batch_size': 16,
        'learning_rate': 1e-3
    }
    
    try:
        model = saits_config['model_class'](**hparams)
        print(f"✅ Model initialized: {model}")
        
        # Test fit
        print("🚀 Testing model.fit()...")
        model.fit(train_tensor, truth_tensor, epochs=5, batch_size=16)
        print("✅ Model fit completed")
        
        # Test predict
        print("🔮 Testing model.predict()...")
        predictions = model.predict(train_tensor)
        print(f"✅ Predictions shape: {predictions.shape}")
        print(f"✅ Predictions type: {type(predictions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_saits_model()
    if success:
        print("✅ SAITS model test passed!")
    else:
        print("❌ SAITS model test failed!")
