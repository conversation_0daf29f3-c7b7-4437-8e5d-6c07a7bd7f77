#!/usr/bin/env python3
"""
Test script to verify all required dependencies for Phase 1 implementation.
"""

def test_pypots():
    """Test PyPOTS installation and core models."""
    try:
        from pypots.imputation import SAITS, BRITS
        print("✅ PyPOTS models (SAITS, BRITS) available")
        return True
    except ImportError as e:
        print(f"❌ PyPOTS not available: {e}")
        return False

def test_monai():
    """Test MONAI installation."""
    try:
        from monai.networks.nets import UNet
        print("✅ MONAI UNet available")
        return True
    except ImportError as e:
        print(f"❌ MONAI not available: {e}")
        return False

def test_transformers():
    """Test transformers library."""
    try:
        import transformers
        print(f"✅ Transformers available (version: {transformers.__version__})")
        return True
    except ImportError as e:
        print(f"❌ Transformers not available: {e}")
        return False

def test_einops():
    """Test einops library."""
    try:
        import einops
        print(f"✅ Einops available (version: {einops.__version__})")
        return True
    except ImportError as e:
        print(f"❌ Einops not available: {e}")
        return False

def test_existing_models():
    """Test that existing models still work."""
    try:
        from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
        print("✅ Existing models (SimpleAutoencoder, SimpleUNet) available")
        return True
    except ImportError as e:
        print(f"❌ Existing models not available: {e}")
        return False

def test_core_dependencies():
    """Test core dependencies."""
    try:
        import torch
        import numpy as np
        import pandas as pd
        import sklearn
        print(f"✅ Core dependencies available:")
        print(f"   - PyTorch: {torch.__version__}")
        print(f"   - NumPy: {np.__version__}")
        print(f"   - Pandas: {pd.__version__}")
        print(f"   - Scikit-learn: {sklearn.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Core dependencies issue: {e}")
        return False

def main():
    """Run all dependency tests."""
    print("🔍 Testing dependencies for Phase 1 implementation...")
    print("=" * 60)
    
    tests = [
        ("Core Dependencies", test_core_dependencies),
        ("Existing Models", test_existing_models),
        ("PyPOTS", test_pypots),
        ("MONAI", test_monai),
        ("Transformers", test_transformers),
        ("Einops", test_einops),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}:")
        results[test_name] = test_func()
    
    print("\n" + "=" * 60)
    print("📊 DEPENDENCY TEST SUMMARY:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} : {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 All dependencies are available! Ready for Phase 1 implementation.")
    else:
        print("⚠️  Some dependencies are missing. Install them before proceeding:")
        if not results.get("PyPOTS", True):
            print("   pip install pypots>=0.2.0")
        if not results.get("MONAI", True):
            print("   pip install monai>=0.9.0")
        if not results.get("Transformers", True):
            print("   pip install transformers>=4.30.0")
        if not results.get("Einops", True):
            print("   pip install einops>=0.7.0")
    
    return all_passed

if __name__ == "__main__":
    main()
