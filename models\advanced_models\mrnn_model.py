"""
Multi-Resolution RNN (mRNN) Model Implementation for Time Series Imputation
Custom multi-resolution RNN architecture with hierarchical processing for well log data

This module implements a multi-resolution RNN model with hierarchical feature extraction,
multi-scale temporal processing, and attention mechanisms for feature fusion.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
import warnings

from .base_model import BaseAdvancedModel

class AttentionMechanism(nn.Module):
    """Attention mechanism for feature fusion."""
    
    def __init__(self, input_dim: int, attention_dim: int):
        super().__init__()
        self.attention_dim = attention_dim
        
        self.attention_layer = nn.Sequential(
            nn.Linear(input_dim, attention_dim),
            nn.Tanh(),
            nn.Linear(attention_dim, 1)
        )
        
    def forward(self, features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply attention mechanism to features.
        
        Args:
            features: Input features (batch, seq_len, feature_dim)
            
        Returns:
            Tuple of (attended_features, attention_weights)
        """
        # Calculate attention scores
        attention_scores = self.attention_layer(features)  # (batch, seq_len, 1)
        attention_weights = F.softmax(attention_scores, dim=1)  # (batch, seq_len, 1)
        
        # Apply attention weights
        attended_features = torch.sum(features * attention_weights, dim=1)  # (batch, feature_dim)
        
        return attended_features, attention_weights.squeeze(-1)

class MultiResolutionLSTM(nn.Module):
    """Multi-resolution LSTM layer with different temporal scales."""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], 
                 bidirectional: bool = True, dropout: float = 0.2):
        super().__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.bidirectional = bidirectional
        self.n_resolutions = len(hidden_sizes)
        
        # Create LSTM layers for different resolutions
        self.lstm_layers = nn.ModuleList()
        for hidden_size in hidden_sizes:
            lstm = nn.LSTM(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=1,
                batch_first=True,
                bidirectional=bidirectional,
                dropout=dropout if hidden_size > 1 else 0
            )
            self.lstm_layers.append(lstm)
        
        # Calculate output dimension
        self.output_dim = sum(hidden_sizes) * (2 if bidirectional else 1)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        Forward pass through multi-resolution LSTM.
        
        Args:
            x: Input tensor (batch, seq_len, input_size)
            
        Returns:
            Tuple of (concatenated_outputs, individual_outputs)
        """
        outputs = []
        hidden_states = []
        
        for i, lstm_layer in enumerate(self.lstm_layers):
            # Apply different downsampling for different resolutions
            if i > 0:
                # Downsample input for higher resolution levels
                stride = 2 ** i
                if x.size(1) >= stride:
                    x_downsampled = x[:, ::stride, :]
                else:
                    x_downsampled = x
            else:
                x_downsampled = x
            
            # LSTM forward pass
            lstm_out, (h_n, c_n) = lstm_layer(x_downsampled)
            
            # Upsample back to original sequence length if needed
            if lstm_out.size(1) != x.size(1):
                lstm_out = F.interpolate(
                    lstm_out.transpose(1, 2), 
                    size=x.size(1), 
                    mode='linear', 
                    align_corners=False
                ).transpose(1, 2)
            
            outputs.append(lstm_out)
            hidden_states.append(h_n)
        
        # Concatenate outputs from all resolutions
        concatenated_output = torch.cat(outputs, dim=-1)
        
        return concatenated_output, hidden_states

class MRNNNet(nn.Module):
    """Multi-Resolution RNN Network for time series imputation."""
    
    def __init__(self, n_features: int, sequence_len: int, 
                 hidden_sizes: List[int] = [64, 128, 256],
                 n_layers: int = 3, bidirectional: bool = True,
                 attention_dim: int = 128, dropout: float = 0.2):
        super().__init__()
        
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.hidden_sizes = hidden_sizes
        self.n_layers = n_layers
        self.bidirectional = bidirectional
        self.attention_dim = attention_dim
        
        # Input projection
        self.input_projection = nn.Linear(n_features, hidden_sizes[0])
        
        # Multi-resolution LSTM layers
        self.mrnn_layers = nn.ModuleList()
        current_input_size = hidden_sizes[0]
        
        for layer_idx in range(n_layers):
            mrnn_layer = MultiResolutionLSTM(
                input_size=current_input_size,
                hidden_sizes=hidden_sizes,
                bidirectional=bidirectional,
                dropout=dropout
            )
            self.mrnn_layers.append(mrnn_layer)
            current_input_size = mrnn_layer.output_dim
        
        # Attention mechanism for feature fusion
        self.attention = AttentionMechanism(current_input_size, attention_dim)
        
        # Output layers
        self.output_layers = nn.Sequential(
            nn.Linear(current_input_size, hidden_sizes[-1]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_sizes[-1], hidden_sizes[0]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_sizes[0], n_features)
        )
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(current_input_size)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the mRNN network.
        
        Args:
            x: Input tensor (batch, seq_len, n_features)
            
        Returns:
            Output tensor (batch, seq_len, n_features)
        """
        # Input projection
        x = self.input_projection(x)
        
        # Multi-resolution LSTM layers
        for mrnn_layer in self.mrnn_layers:
            x, _ = mrnn_layer(x)
            x = self.layer_norm(x)
        
        # Apply attention mechanism
        attended_features, attention_weights = self.attention(x)
        
        # Expand attended features back to sequence length
        attended_features = attended_features.unsqueeze(1).expand(-1, self.sequence_len, -1)
        
        # Combine with original features
        combined_features = x + attended_features
        
        # Output projection
        output = self.output_layers(combined_features)
        
        return output

class MRNNModel(BaseAdvancedModel):
    """
    Multi-Resolution RNN model for well log imputation.
    
    Implements a multi-resolution RNN architecture with hierarchical processing,
    multi-scale temporal processing, and attention mechanisms for feature fusion.
    """
    
    def __init__(self, n_features: int = 4, sequence_len: int = 64,
                 hidden_sizes: List[int] = None, n_layers: int = 3,
                 bidirectional: bool = True, attention_dim: int = 128,
                 dropout: float = 0.2, epochs: int = 75, batch_size: int = 32,
                 learning_rate: float = 1e-3, **kwargs):
        """
        Initialize mRNN model for well log imputation.
        
        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            hidden_sizes: List of hidden sizes for different resolutions
            n_layers: Number of resolution levels
            bidirectional: Whether to use bidirectional LSTM
            attention_dim: Attention mechanism dimension
            dropout: Dropout rate for regularization
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)
        
        if hidden_sizes is None:
            hidden_sizes = [64, 128, 256]
        
        self.hidden_sizes = hidden_sizes
        self.n_layers = n_layers
        self.bidirectional = bidirectional
        self.attention_dim = attention_dim
        self.dropout = dropout
        
        # Validate parameters
        self._validate_parameters()
        
        # Training components
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.criterion = nn.MSELoss()
        self.optimizer = None
        
        print(f"🎯 mRNN Model Configuration:")
        print(f"   - Hidden sizes: {hidden_sizes}")
        print(f"   - Resolution levels: {n_layers}")
        print(f"   - Bidirectional: {bidirectional}")
        print(f"   - Attention dim: {attention_dim}")
        print(f"   - Dropout rate: {dropout}")
        print(f"   - Device: {self.device}")
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if len(self.hidden_sizes) < 1:
            raise ValueError("At least one hidden size must be specified")
        
        if self.n_layers < 1 or self.n_layers > 5:
            print(f"⚠️ Warning: n_layers={self.n_layers} may not be optimal (recommended: 1-5)")
        
        if self.attention_dim < 32 or self.attention_dim > 512:
            print(f"⚠️ Warning: attention_dim={self.attention_dim} may not be optimal (recommended: 32-512)")
        
        if self.n_features < 2:
            raise ValueError("mRNN requires at least 2 features for meaningful processing")
        
        if any(h < 16 or h > 1024 for h in self.hidden_sizes):
            print(f"⚠️ Warning: Some hidden sizes may not be optimal (recommended: 16-1024)")
    
    def _initialize_model(self) -> None:
        """Initialize the mRNN model."""
        try:
            print(f"🔧 Initializing mRNN model...")
            
            self.model = MRNNNet(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=self.hidden_sizes,
                n_layers=self.n_layers,
                bidirectional=self.bidirectional,
                attention_dim=self.attention_dim,
                dropout=self.dropout
            ).to(self.device)
            
            # Initialize optimizer
            self.optimizer = torch.optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=1e-5
            )
            
            print(f"✅ mRNN model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"❌ Failed to initialize mRNN model: {e}")
            raise RuntimeError(f"mRNN model initialization failed: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data for mRNN training/prediction."""
        # Convert to device
        data = data.to(self.device)

        if truth_data is not None:
            truth_data = truth_data.to(self.device)
            return {
                'input_data': data,
                'target_data': truth_data,
                'mask': ~torch.isnan(data)
            }
        else:
            return {
                'input_data': data,
                'mask': ~torch.isnan(data)
            }

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the mRNN model.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        import time

        # Validate input data
        if not self._validate_input_data(train_data) or not self._validate_input_data(truth_data):
            raise ValueError("Invalid input data format")

        # Initialize model if not already done
        if self.model is None:
            self._initialize_model()

        # Use provided parameters or defaults
        epochs = epochs or self.epochs
        batch_size = batch_size or self.batch_size

        print(f"🚀 Training mRNN for {epochs} epochs...")
        print(f"   Training data shape: {train_data.shape}")
        print(f"   Missing values: {torch.isnan(train_data).sum().item()}")

        # Record start time
        start_time = time.time()

        # Prepare training data
        train_set = self._prepare_data(train_data, truth_data)
        input_data = train_set['input_data']
        target_data = train_set['target_data']
        mask = train_set['mask']

        # Replace NaN values with zeros for training
        input_data_clean = torch.where(torch.isnan(input_data), torch.zeros_like(input_data), input_data)

        # Training loop
        self.model.train()
        self.training_history['loss'] = []

        try:
            for epoch in range(epochs):
                epoch_start = time.time()
                total_loss = 0.0

                # Create batches
                n_samples = input_data_clean.size(0)
                n_batches = (n_samples + batch_size - 1) // batch_size

                for batch_idx in range(n_batches):
                    start_idx = batch_idx * batch_size
                    end_idx = min(start_idx + batch_size, n_samples)

                    batch_input = input_data_clean[start_idx:end_idx]
                    batch_target = target_data[start_idx:end_idx]
                    batch_mask = mask[start_idx:end_idx]

                    # Forward pass
                    self.optimizer.zero_grad()
                    predictions = self.model(batch_input)

                    # Calculate loss only on observed values
                    loss = self._calculate_masked_loss(predictions, batch_target, batch_mask)

                    # Backward pass
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    self.optimizer.step()

                    total_loss += loss.item()

                # Record epoch metrics
                avg_loss = total_loss / n_batches
                epoch_time = time.time() - epoch_start
                self.training_history['loss'].append(avg_loss)
                self.training_history['epoch_times'].append(epoch_time)

                if (epoch + 1) % 10 == 0 or epoch == 0:
                    print(f"   Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}, Time: {epoch_time:.2f}s")

            self.is_fitted = True

            # Record training time
            training_time = time.time() - start_time
            self.training_history['total_training_time'] = training_time

            print(f"✅ mRNN training completed in {training_time:.2f} seconds!")
            print(f"   Final loss: {self.training_history['loss'][-1]:.6f}")

        except Exception as e:
            print(f"❌ Training failed: {e}")
            raise

    def _calculate_masked_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                              mask: torch.Tensor) -> torch.Tensor:
        """Calculate loss only on observed values."""
        # Apply mask to both predictions and targets
        masked_predictions = predictions[mask]
        masked_targets = targets[mask]

        if masked_predictions.numel() == 0:
            # If no observed values, return zero loss
            return torch.tensor(0.0, device=predictions.device, requires_grad=True)

        return self.criterion(masked_predictions, masked_targets)

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using the mRNN.

        Args:
            data: Input data with missing values

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        # Validate input data
        if not self._validate_input_data(data):
            raise ValueError("Invalid input data format")

        print(f"🔮 Predicting with mRNN...")
        print(f"   Input data shape: {data.shape}")

        # Prepare test data
        test_set = self._prepare_data(data)
        input_data = test_set['input_data']
        mask = test_set['mask']

        # Replace NaN values with zeros for prediction
        input_data_clean = torch.where(torch.isnan(input_data), torch.zeros_like(input_data), input_data)

        # Prediction
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(input_data_clean)

            # Combine original observed values with predictions for missing values
            result = torch.where(mask, input_data, predictions)

        print(f"✅ Prediction completed")
        return result.cpu()

    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        if self.model is not None:
            return sum(p.numel() for p in self.model.parameters())

        # Rough estimation for mRNN model
        total_params = 0

        # Input projection
        total_params += self.n_features * self.hidden_sizes[0]

        # Multi-resolution LSTM layers
        for layer_idx in range(self.n_layers):
            for hidden_size in self.hidden_sizes:
                # LSTM parameters (input_size, hidden_size, bidirectional)
                input_size = self.hidden_sizes[0] if layer_idx == 0 else sum(self.hidden_sizes) * (2 if self.bidirectional else 1)
                lstm_params = 4 * (input_size + hidden_size + 1) * hidden_size  # 4 gates
                if self.bidirectional:
                    lstm_params *= 2
                total_params += lstm_params

        # Attention mechanism
        final_hidden_size = sum(self.hidden_sizes) * (2 if self.bidirectional else 1)
        attention_params = (
            final_hidden_size * self.attention_dim +
            self.attention_dim * 1
        )
        total_params += attention_params

        # Output layers
        output_params = (
            final_hidden_size * self.hidden_sizes[-1] +
            self.hidden_sizes[-1] * self.hidden_sizes[0] +
            self.hidden_sizes[0] * self.n_features
        )
        total_params += output_params

        return total_params

    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32

        # Activation memory for multi-resolution processing
        max_hidden_size = max(self.hidden_sizes)
        activation_memory = (
            self.batch_size * self.sequence_len * max_hidden_size *
            self.n_layers * 4 / (1024 * 1024)  # 4 for different LSTM gates
        )

        return param_memory + activation_memory

    def get_attention_weights(self, data: torch.Tensor) -> Optional[np.ndarray]:
        """
        Extract attention weights for visualization.

        Args:
            data: Input data tensor

        Returns:
            Attention weights array or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting attention weights")
            return None

        try:
            # This would require modification to store attention weights during forward pass
            # For now, return None and implement in future versions
            print("ℹ️ Attention weight extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract attention weights: {e}")
            return None

    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'hidden_sizes': self.hidden_sizes,
            'resolution_levels': self.n_layers,
            'bidirectional': self.bidirectional,
            'attention_dimension': self.attention_dim,
            'complexity_score': 3,  # High complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'medium',
            'performance_tier': 'high'
        }
