"""
Dataset classes for imputation models.
"""

import torch
import numpy as np
from torch.utils.data import Dataset
from typing import Dict, Union


class DatasetForImputation(Dataset):
    """
    Dataset class for imputation tasks.
    
    This class handles data with missing values for training imputation models.
    It expects a dictionary with keys: 'X', 'X_intact', 'indicating_mask'
    """
    
    def __init__(self, data: Dict[str, Union[np.ndarray, torch.Tensor]]):
        """
        Initialize the dataset.
        
        Args:
            data: Dictionary containing:
                - 'X': Data with missing values (NaN replaced with 0 or other values)
                - 'X_intact': Original data without artificial missing values
                - 'indicating_mask': Mask indicating where artificial missing values were introduced (1 = missing, 0 = present)
        """
        self.data = data
        
        # Convert numpy arrays to tensors if needed
        for key, value in self.data.items():
            if isinstance(value, np.ndarray):
                self.data[key] = torch.from_numpy(value).float()
            elif isinstance(value, torch.Tensor):
                self.data[key] = value.float()
        
        # Validate data shapes
        self._validate_data()
        
        # Create missing_mask (inverse of indicating_mask)
        if 'indicating_mask' in self.data:
            self.data['missing_mask'] = 1.0 - self.data['indicating_mask']
        
        self.length = len(self.data['X'])
    
    def _validate_data(self):
        """Validate that all data arrays have consistent shapes."""
        if 'X' not in self.data:
            raise ValueError("Data must contain 'X' key")
        
        x_shape = self.data['X'].shape
        
        for key, value in self.data.items():
            if key != 'X' and hasattr(value, 'shape'):
                if value.shape != x_shape:
                    raise ValueError(f"Shape mismatch: {key} has shape {value.shape}, but X has shape {x_shape}")
    
    def __len__(self):
        """Return the number of samples in the dataset."""
        return self.length
    
    def __getitem__(self, idx):
        """
        Get a single sample from the dataset.
        
        Args:
            idx: Index of the sample to retrieve
            
        Returns:
            Tuple containing (indices, X, X_intact, missing_mask, indicating_mask)
        """
        # Create indices tensor
        indices = torch.tensor([idx], dtype=torch.long)
        
        # Get data for this index
        X = self.data['X'][idx]
        X_intact = self.data.get('X_intact', X)[idx]  # Use X if X_intact not available
        missing_mask = self.data.get('missing_mask', torch.ones_like(X))[idx]
        indicating_mask = self.data.get('indicating_mask', torch.zeros_like(X))[idx]
        
        # Handle NaN values in X by replacing with 0
        if torch.any(torch.isnan(X)):
            X = torch.nan_to_num(X, nan=0.0)
        
        # Ensure all tensors are float type
        X = X.float()
        X_intact = X_intact.float()
        missing_mask = missing_mask.float()
        indicating_mask = indicating_mask.float()
        
        return indices, X, X_intact, missing_mask, indicating_mask


def create_imputation_dataset(X: np.ndarray, missing_rate: float = 0.2, random_seed: int = 42) -> Dict[str, np.ndarray]:
    """
    Create a dataset for imputation by introducing artificial missing values.
    
    Args:
        X: Original data array of shape (n_samples, sequence_length, n_features)
        missing_rate: Fraction of values to make missing
        random_seed: Random seed for reproducibility
        
    Returns:
        Dictionary with keys 'X', 'X_intact', 'indicating_mask'
    """
    np.random.seed(random_seed)
    
    X_intact = X.copy()
    X_with_missing = X.copy()
    
    # Create indicating mask (1 where we introduce missing values)
    indicating_mask = np.zeros_like(X)
    
    # Randomly select positions to make missing
    total_elements = X.size
    n_missing = int(total_elements * missing_rate)
    
    # Get random indices
    flat_indices = np.random.choice(total_elements, size=n_missing, replace=False)
    
    # Convert flat indices to multi-dimensional indices
    multi_indices = np.unravel_index(flat_indices, X.shape)
    
    # Set missing values
    X_with_missing[multi_indices] = np.nan
    indicating_mask[multi_indices] = 1.0
    
    # Replace NaN with 0 for model input
    X_with_missing = np.nan_to_num(X_with_missing, nan=0.0)
    
    return {
        'X': X_with_missing,
        'X_intact': X_intact,
        'indicating_mask': indicating_mask
    }
