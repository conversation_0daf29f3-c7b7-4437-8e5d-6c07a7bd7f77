# Phase 2: Core Models Implementation (Week 2)
## Advanced Deep Learning Models - Core Implementation Phase

### 🎯 **Phase Objectives**
- Implement SAITS model (Self-Attention Imputation Time Series) - **PRIORITY 1**
- Implement BRITS model (Bidirectional Recurrent Imputation) - **PRIORITY 2**
- Add models to registry with comprehensive fallback mechanisms
- Establish initial performance benchmarking framework

### 📋 **Phase 2 Tasks Breakdown**

#### **Day 1-3: SAITS Model Implementation (Priority 1)**

##### Task 2.1: SAITS Model Core Implementation
```python
# models/advanced_models/saits_model.py
"""
SAITS (Self-Attention Imputation Time Series) Model Implementation
Based on PyPOTS framework with custom adaptations for well log data
"""

import torch
import numpy as np
from pypots.imputation import SAITS
from pypots.optim import Adam
from .base_model import BaseAdvancedModel
from typing import Dict, Any, Optional

class SAITSModel(BaseAdvancedModel):
    """
    SAITS model wrapper for well log imputation.
    Provides compatibility with existing workflow while leveraging
    state-of-the-art self-attention mechanisms.
    """

    def __init__(self, n_features=4, sequence_len=64, n_layers=2,
                 d_model=256, n_heads=4, epochs=50, batch_size=32,
                 learning_rate=1e-3, dropout=0.1, **kwargs):
        """
        Initialize SAITS model with well log specific configurations.

        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            n_layers: Number of transformer layers
            d_model: Model dimension for attention mechanism
            n_heads: Number of attention heads
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            dropout: Dropout rate for regularization
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, **kwargs)
        
        self.n_layers = n_layers
        self.d_model = d_model
        self.n_heads = n_heads
        self.learning_rate = learning_rate
        self.dropout = dropout
        
        # Validate parameters
        self._validate_parameters()
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.d_model % self.n_heads != 0:
            raise ValueError(f"d_model ({self.d_model}) must be divisible by n_heads ({self.n_heads})")
        
        if self.sequence_len < 16:
            print("⚠️ Warning: sequence_len < 16 may not be optimal for attention mechanisms")
        
        if self.n_features < 2:
            raise ValueError("SAITS requires at least 2 features for meaningful attention")

    def _initialize_model(self) -> None:
        """Initialize the PyPOTS SAITS model."""
        try:
            self.model = SAITS(
                n_steps=self.sequence_len,
                n_features=self.n_features,
                n_layers=self.n_layers,
                d_model=self.d_model,
                d_inner=self.d_model // 2,  # Inner dimension for feed-forward
                n_heads=self.n_heads,
                d_k=self.d_model // self.n_heads,  # Key dimension
                d_v=self.d_model // self.n_heads,  # Value dimension
                dropout=self.dropout,
                attn_dropout=self.dropout,
                diagonal_attention_mask=True,  # Causal attention for time series
                ORT_weight=1.0,  # Original Reconstruction Task weight
                MIT_weight=1.0,  # Masked Imputation Task weight
                batch_size=self.batch_size,
                epochs=self.epochs,
                patience=15,  # Early stopping patience
                optimizer=Adam(lr=self.learning_rate),
                device='cpu',  # Will auto-detect GPU if available
                saving_path=None,  # No automatic saving
                model_saving_strategy=None
            )
            print(f"✅ SAITS model initialized with {self.n_layers} layers, {self.d_model} dimensions")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize SAITS model: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in PyPOTS format for SAITS."""
        return self._prepare_pypots_data(data, truth_data)
    
    def get_attention_weights(self, data: torch.Tensor) -> Optional[np.ndarray]:
        """
        Extract attention weights for visualization (if supported).
        
        Args:
            data: Input data tensor
            
        Returns:
            Attention weights array or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting attention weights")
            return None
            
        try:
            # This would require modification of PyPOTS SAITS to expose attention weights
            # For now, return None and implement in future versions
            print("ℹ️ Attention weight extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract attention weights: {e}")
            return None
    
    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics."""
        total_params = (
            self.n_layers * (
                # Self-attention parameters
                3 * self.d_model * self.d_model +  # Q, K, V projections
                self.d_model * self.d_model +      # Output projection
                # Feed-forward parameters
                self.d_model * (self.d_model // 2) + 
                (self.d_model // 2) * self.d_model
            )
        )
        
        return {
            'total_parameters': total_params,
            'attention_heads': self.n_heads,
            'transformer_layers': self.n_layers,
            'model_dimension': self.d_model,
            'complexity_score': 3  # High complexity
        }
```

##### Task 2.2: SAITS Model Testing & Validation
```python
# tests/test_saits_model.py
import unittest
import torch
import numpy as np
from models.advanced_models.saits_model import SAITSModel

class TestSAITSModel(unittest.TestCase):
    """Test suite for SAITS model implementation."""
    
    def setUp(self):
        """Set up test data."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 8
        
        # Create test data
        self.test_data = torch.randn(self.batch_size, self.sequence_len, self.n_features)
        self.test_data[self.test_data > 0.5] = float('nan')  # Add missing values
        self.truth_data = torch.randn(self.batch_size, self.sequence_len, self.n_features)
    
    def test_model_initialization(self):
        """Test SAITS model initialization."""
        model = SAITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            n_layers=1,  # Small for testing
            d_model=64,  # Small for testing
            n_heads=2,
            epochs=2,
            batch_size=self.batch_size
        )
        
        self.assertEqual(model.n_features, self.n_features)
        self.assertEqual(model.sequence_len, self.sequence_len)
        self.assertFalse(model.is_fitted)
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        # Test invalid d_model/n_heads combination
        with self.assertRaises(ValueError):
            SAITSModel(d_model=100, n_heads=3)  # 100 not divisible by 3
        
        # Test invalid n_features
        with self.assertRaises(ValueError):
            SAITSModel(n_features=1)  # Too few features
    
    def test_training_and_prediction(self):
        """Test model training and prediction."""
        model = SAITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            n_layers=1,
            d_model=64,
            n_heads=2,
            epochs=1,  # Quick test
            batch_size=self.batch_size
        )
        
        # Test training
        model.fit(self.test_data, self.truth_data, epochs=1)
        self.assertTrue(model.is_fitted)
        
        # Test prediction
        predictions = model.predict(self.test_data)
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())
    
    def test_model_complexity(self):
        """Test model complexity calculation."""
        model = SAITSModel(n_layers=2, d_model=128, n_heads=4)
        complexity = model.get_model_complexity()
        
        self.assertIn('total_parameters', complexity)
        self.assertIn('complexity_score', complexity)
        self.assertEqual(complexity['complexity_score'], 3)  # High complexity

if __name__ == '__main__':
    unittest.main()
```

#### **Day 4-6: BRITS Model Implementation (Priority 2)**

##### Task 2.3: BRITS Model Core Implementation
```python
# models/advanced_models/brits_model.py
"""
BRITS (Bidirectional Recurrent Imputation Time Series) Model Implementation
Specialized for temporal dependencies in well log data
"""

import torch
import numpy as np
from pypots.imputation import BRITS
from pypots.optim import Adam
from .base_model import BaseAdvancedModel
from typing import Dict, Any, Optional

class BRITSModel(BaseAdvancedModel):
    """
    BRITS model wrapper for well log imputation.
    Leverages bidirectional RNN for temporal pattern modeling.
    """

    def __init__(self, n_features=4, sequence_len=64, rnn_hidden_size=128,
                 epochs=50, batch_size=32, learning_rate=1e-3, **kwargs):
        """
        Initialize BRITS model.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            rnn_hidden_size: Hidden size for RNN layers
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, **kwargs)
        
        self.rnn_hidden_size = rnn_hidden_size
        self.learning_rate = learning_rate
        
        # Validate parameters
        self._validate_parameters()
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.rnn_hidden_size < 16:
            print("⚠️ Warning: rnn_hidden_size < 16 may limit model capacity")
        
        if self.sequence_len < 8:
            print("⚠️ Warning: sequence_len < 8 may not capture temporal patterns effectively")

    def _initialize_model(self) -> None:
        """Initialize the PyPOTS BRITS model."""
        try:
            self.model = BRITS(
                n_steps=self.sequence_len,
                n_features=self.n_features,
                rnn_hidden_size=self.rnn_hidden_size,
                batch_size=self.batch_size,
                epochs=self.epochs,
                patience=15,  # Early stopping patience
                optimizer=Adam(lr=self.learning_rate),
                device='cpu',  # Will auto-detect GPU if available
                saving_path=None,
                model_saving_strategy=None
            )
            print(f"✅ BRITS model initialized with hidden size {self.rnn_hidden_size}")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize BRITS model: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in PyPOTS format for BRITS."""
        return self._prepare_pypots_data(data, truth_data)
    
    def get_temporal_patterns(self, data: torch.Tensor) -> Optional[Dict[str, np.ndarray]]:
        """
        Extract temporal patterns learned by the model.
        
        Args:
            data: Input data tensor
            
        Returns:
            Dictionary with temporal pattern information or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting temporal patterns")
            return None
            
        try:
            # This would require modification of PyPOTS BRITS to expose hidden states
            # For now, return None and implement in future versions
            print("ℹ️ Temporal pattern extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract temporal patterns: {e}")
            return None
    
    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics."""
        # Estimate parameters for bidirectional RNN
        rnn_params = (
            # Forward RNN
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Backward RNN  
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Output layers
            2 * self.rnn_hidden_size * self.n_features
        )
        
        return {
            'total_parameters': rnn_params,
            'rnn_hidden_size': self.rnn_hidden_size,
            'bidirectional': True,
            'complexity_score': 2  # Medium complexity
        }
```

#### **Day 7: Model Registry Integration & Testing**

##### Task 2.4: Enhanced Model Registry Integration
```python
# ml_core.py - Add to existing MODEL_REGISTRY (ADDITION)

# Add advanced models to registry if available
if ADVANCED_MODELS_AVAILABLE:
    ADVANCED_MODEL_CONFIGS = {
        'saits': {
            'name': 'SAITS (Self-Attention)',
            'model_class': SAITSModel if ADVANCED_MODELS_STATUS.get('saits', False) else None,
            'type': 'deep_advanced',
            'description': 'State-of-the-art self-attention model for time series imputation',
            'performance_tier': 'highest',
            'computational_cost': 'high',
            'hyperparameters': {
                'sequence_len': {'type': int, 'default': 64, 'range': [32, 128]},
                'n_features': {'type': int, 'default': 4},
                'n_layers': {'type': int, 'default': 2, 'range': [1, 4]},
                'd_model': {'type': int, 'default': 256, 'range': [128, 512]},
                'n_heads': {'type': int, 'default': 4, 'range': [2, 8]},
                'epochs': {'type': int, 'default': 50, 'range': [20, 100]},
                'batch_size': {'type': int, 'default': 32, 'range': [16, 64]},
                'learning_rate': {'type': float, 'default': 1e-3, 'range': [1e-4, 1e-2]},
                'dropout': {'type': float, 'default': 0.1, 'range': [0.0, 0.3]},
            },
            'fixed_params': {},
            'recommended_for': ['complex_patterns', 'long_sequences', 'high_accuracy'],
            'requires_gpu': False,
            'memory_intensive': True
        },
        'brits': {
            'name': 'BRITS (Bidirectional RNN)',
            'model_class': BRITSModel if ADVANCED_MODELS_STATUS.get('brits', False) else None,
            'type': 'deep_advanced',
            'description': 'Bidirectional RNN specialized for temporal dependencies',
            'performance_tier': 'high',
            'computational_cost': 'medium',
            'hyperparameters': {
                'sequence_len': {'type': int, 'default': 64, 'range': [32, 128]},
                'n_features': {'type': int, 'default': 4},
                'rnn_hidden_size': {'type': int, 'default': 128, 'range': [64, 256]},
                'epochs': {'type': int, 'default': 50, 'range': [20, 100]},
                'batch_size': {'type': int, 'default': 32, 'range': [16, 64]},
                'learning_rate': {'type': float, 'default': 1e-3, 'range': [1e-4, 1e-2]},
            },
            'fixed_params': {},
            'recommended_for': ['temporal_patterns', 'sequential_data', 'medium_complexity'],
            'requires_gpu': False,
            'memory_intensive': False
        }
    }
    
    # Safely add advanced models to registry
    for model_key, config in ADVANCED_MODEL_CONFIGS.items():
        if config['model_class'] is not None:
            MODEL_REGISTRY[model_key] = config
            print(f"✅ Added {config['name']} to model registry")
        else:
            print(f"⚠️ Skipped {config['name']} - dependencies not available")

# Enhanced model selection with performance tiers
def get_models_by_performance_tier(tier: str = 'all') -> List[str]:
    """
    Get models filtered by performance tier.
    
    Args:
        tier: 'highest', 'high', 'medium', 'standard', or 'all'
        
    Returns:
        List of model keys matching the tier
    """
    if tier == 'all':
        return list(MODEL_REGISTRY.keys())
    
    matching_models = []
    for model_key, config in MODEL_REGISTRY.items():
        if config.get('performance_tier', 'standard') == tier:
            matching_models.append(model_key)
    
    return matching_models

def get_computational_cost_estimate(model_key: str) -> str:
    """Get computational cost estimate for a model."""
    if model_key in MODEL_REGISTRY:
        return MODEL_REGISTRY[model_key].get('computational_cost', 'unknown')
    return 'unknown'
```

### 🧪 **Phase 2 Testing & Validation**

##### Task 2.5: Comprehensive Model Testing
```python
# tests/test_phase2_core_models.py
import unittest
import torch
import numpy as np
from models.advanced_models import ADVANCED_MODELS_STATUS

class TestPhase2CoreModels(unittest.TestCase):
    """Test suite for Phase 2 core model implementations."""
    
    def setUp(self):
        """Set up test data."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 8
        
        # Create realistic well log test data
        self.test_data = torch.randn(self.batch_size, self.sequence_len, self.n_features)
        self.test_data = torch.abs(self.test_data)  # Well logs are typically positive
        self.test_data[self.test_data > 2.0] = float('nan')  # Add missing values
        self.truth_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))
    
    @unittest.skipUnless(ADVANCED_MODELS_STATUS.get('saits', False), "SAITS not available")
    def test_saits_full_workflow(self):
        """Test complete SAITS workflow."""
        from models.advanced_models.saits_model import SAITSModel
        
        model = SAITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            n_layers=1,
            d_model=64,
            n_heads=2,
            epochs=2,
            batch_size=self.batch_size
        )
        
        # Test training
        model.fit(self.test_data, self.truth_data, epochs=2)
        self.assertTrue(model.is_fitted)
        
        # Test prediction
        predictions = model.predict(self.test_data)
        self.assertEqual(predictions.shape, self.test_data.shape)
        
        # Test evaluation
        missing_mask = torch.isnan(self.test_data)
        metrics = model.evaluate_imputation(self.truth_data, predictions, missing_mask)
        self.assertIn('mae', metrics)
        self.assertIn('rmse', metrics)
        self.assertIn('r2', metrics)
    
    @unittest.skipUnless(ADVANCED_MODELS_STATUS.get('brits', False), "BRITS not available")
    def test_brits_full_workflow(self):
        """Test complete BRITS workflow."""
        from models.advanced_models.brits_model import BRITSModel
        
        model = BRITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            rnn_hidden_size=64,
            epochs=2,
            batch_size=self.batch_size
        )
        
        # Test training
        model.fit(self.test_data, self.truth_data, epochs=2)
        self.assertTrue(model.is_fitted)
        
        # Test prediction
        predictions = model.predict(self.test_data)
        self.assertEqual(predictions.shape, self.test_data.shape)
        
        # Test complexity calculation
        complexity = model.get_model_complexity()
        self.assertIn('total_parameters', complexity)
        self.assertEqual(complexity['complexity_score'], 2)
    
    def test_model_registry_integration(self):
        """Test that models are properly integrated into registry."""
        from ml_core import MODEL_REGISTRY, get_models_by_performance_tier
        
        # Test registry contains new models (if available)
        if ADVANCED_MODELS_STATUS.get('saits', False):
            self.assertIn('saits', MODEL_REGISTRY)
            self.assertEqual(MODEL_REGISTRY['saits']['type'], 'deep_advanced')
        
        if ADVANCED_MODELS_STATUS.get('brits', False):
            self.assertIn('brits', MODEL_REGISTRY)
            self.assertEqual(MODEL_REGISTRY['brits']['type'], 'deep_advanced')
        
        # Test performance tier filtering
        highest_tier_models = get_models_by_performance_tier('highest')
        if ADVANCED_MODELS_STATUS.get('saits', False):
            self.assertIn('saits', highest_tier_models)

if __name__ == '__main__':
    unittest.main()
```

### 📊 **Phase 2 Success Criteria**

#### **Technical Validation**
- [ ] SAITS model successfully implemented and tested
- [ ] BRITS model successfully implemented and tested
- [ ] Both models integrate seamlessly with existing workflow
- [ ] Models show expected performance characteristics
- [ ] Comprehensive error handling and validation
- [ ] Memory usage within acceptable limits

#### **Performance Validation**
- [ ] SAITS shows superior performance on complex patterns
- [ ] BRITS demonstrates good temporal dependency modeling
- [ ] Both models outperform basic autoencoder baseline
- [ ] Training times are reasonable for model complexity
- [ ] Prediction accuracy meets expectations

#### **Integration Validation**
- [ ] Models properly registered in MODEL_REGISTRY
- [ ] Safe loading and fallback mechanisms work
- [ ] Hyperparameter validation functions correctly
- [ ] Model recommendation system includes new models

### 🚀 **Phase 2 Deliverables**

1. **SAITS Implementation**: Complete SAITSModel class with PyPOTS integration
2. **BRITS Implementation**: Complete BRITSModel class with RNN architecture
3. **Enhanced Registry**: Updated MODEL_REGISTRY with advanced models
4. **Testing Suite**: Comprehensive tests for both models
5. **Performance Framework**: Initial benchmarking and comparison tools
6. **Documentation**: Model-specific documentation and usage examples

### ➡️ **Transition to Phase 3**

**Prerequisites for Phase 3:**
- [ ] All Phase 2 success criteria met
- [ ] SAITS and BRITS models validated
- [ ] Performance benchmarks established
- [ ] No regression in existing functionality

**Phase 3 Preview:**
- Implement Enhanced UNet (Priority 3)
- Comprehensive testing suite
- Performance benchmarking across all models
- Initial optimization and tuning

---

**Estimated Time**: 7 days
**Risk Level**: Medium (new model implementations)
**Dependencies**: Phase 1 completion, PyPOTS, working PyTorch environment
