#!/usr/bin/env python3
"""
Advanced Models GPU Optimization Test Suite
Tests GPU acceleration for SAITS and BRITS models with comprehensive validation.
"""

import torch
import numpy as np
import pandas as pd
import time
import sys
import os
from typing import Dict, Any, Optional

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_advanced_models_availability():
    """Test availability of advanced models and their dependencies."""
    print("🔍 Testing Advanced Models Availability...")
    
    try:
        # Test PyPOTS availability
        import pypots
        print(f"   ✅ PyPOTS version: {pypots.__version__}")
        
        # Test SAITS model
        from models.advanced_models.saits_model import SAITSModel
        print("   ✅ SAITS model available")
        
        # Test BRITS model
        from models.advanced_models.brits_model import BRITSModel
        print("   ✅ BRITS model available")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Advanced models not available: {e}")
        return False

def test_gpu_device_management():
    """Test GPU device management for advanced models."""
    print("\n🔧 Testing GPU Device Management...")
    
    try:
        from models.advanced_models.saits_model import SAITSModel
        from models.advanced_models.brits_model import BRITSModel
        
        # Test SAITS device management
        print("   Testing SAITS device management...")
        saits = SAITSModel(
            n_features=4,
            sequence_len=32,
            epochs=1,  # Minimal for testing
            batch_size=8,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        print(f"     • SAITS device: {saits.device}")
        print(f"     • GPU manager: {saits.gpu_manager is not None}")
        print(f"     • Fallback manager: {saits.fallback_manager is not None}")
        
        # Test BRITS device management
        print("   Testing BRITS device management...")
        brits = BRITSModel(
            n_features=4,
            sequence_len=32,
            epochs=1,  # Minimal for testing
            batch_size=8,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        print(f"     • BRITS device: {brits.device}")
        print(f"     • GPU manager: {brits.gpu_manager is not None}")
        print(f"     • Fallback manager: {brits.fallback_manager is not None}")
        
        print("   ✅ GPU device management test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ GPU device management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_saits_gpu_training():
    """Test SAITS model GPU training with performance monitoring."""
    print("\n🧠 Testing SAITS GPU Training...")
    
    try:
        from models.advanced_models.saits_model import SAITSModel
        
        # Create sample data
        batch_size = 16
        sequence_len = 32
        n_features = 4
        
        # Generate synthetic well log data
        np.random.seed(42)
        train_data = torch.randn(batch_size, sequence_len, n_features)
        truth_data = train_data.clone()
        
        # Introduce some missing values
        missing_mask = torch.rand_like(train_data) < 0.25
        train_data[missing_mask] = float('nan')
        
        print(f"   • Sample data shape: {train_data.shape}")
        print(f"   • Missing data: {missing_mask.float().mean().item():.1%}")
        
        # Test SAITS training
        saits = SAITSModel(
            n_features=n_features,
            sequence_len=sequence_len,
            epochs=3,  # Short test
            batch_size=8,
            use_mixed_precision=True,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        print(f"     • Device: {saits.device}")
        print(f"     • Mixed precision: {saits.use_mixed_precision and saits.device == 'cuda'}")
        
        # Test training
        start_time = time.time()
        saits.fit(train_data, truth_data)
        training_time = time.time() - start_time
        
        print(f"     • Training completed in {training_time:.2f}s")
        
        # Test prediction
        start_time = time.time()
        predictions = saits.predict(train_data)
        prediction_time = time.time() - start_time
        
        print(f"     • Prediction completed in {prediction_time:.3f}s")
        print(f"     • Output shape: {predictions.shape}")
        print("   ✅ SAITS GPU training test passed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ SAITS GPU training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_brits_gpu_training():
    """Test BRITS model GPU training with performance monitoring."""
    print("\n🔄 Testing BRITS GPU Training...")
    
    try:
        from models.advanced_models.brits_model import BRITSModel
        
        # Create sample data
        batch_size = 16
        sequence_len = 32
        n_features = 4
        
        # Generate synthetic well log data
        np.random.seed(42)
        train_data = torch.randn(batch_size, sequence_len, n_features)
        truth_data = train_data.clone()
        
        # Introduce some missing values
        missing_mask = torch.rand_like(train_data) < 0.25
        train_data[missing_mask] = float('nan')
        
        print(f"   • Sample data shape: {train_data.shape}")
        print(f"   • Missing data: {missing_mask.float().mean().item():.1%}")
        
        # Test BRITS training
        brits = BRITSModel(
            n_features=n_features,
            sequence_len=sequence_len,
            epochs=3,  # Short test
            batch_size=8,
            use_mixed_precision=True,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        print(f"     • Device: {brits.device}")
        print(f"     • Mixed precision: {brits.use_mixed_precision and brits.device == 'cuda'}")
        
        # Test training
        start_time = time.time()
        brits.fit(train_data, truth_data)
        training_time = time.time() - start_time
        
        print(f"     • Training completed in {training_time:.2f}s")
        
        # Test prediction
        start_time = time.time()
        predictions = brits.predict(train_data)
        prediction_time = time.time() - start_time
        
        print(f"     • Prediction completed in {prediction_time:.3f}s")
        print(f"     • Output shape: {predictions.shape}")
        print("   ✅ BRITS GPU training test passed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ BRITS GPU training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_monitoring():
    """Test performance monitoring for advanced models."""
    print("\n📊 Testing Performance Monitoring...")
    
    try:
        from utils.performance_monitor import get_performance_monitor
        from models.advanced_models.saits_model import SAITSModel
        
        # Test performance monitoring
        monitor = get_performance_monitor()
        monitor.reset_metrics()  # Start fresh
        
        # Create a small model for testing
        saits = SAITSModel(
            n_features=4,
            sequence_len=16,
            epochs=1,
            batch_size=4,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        # Create minimal test data
        train_data = torch.randn(4, 16, 4)
        truth_data = train_data.clone()
        train_data[torch.rand_like(train_data) < 0.2] = float('nan')
        
        # Train with monitoring
        saits.fit(train_data, truth_data)
        
        # Get performance summary
        summary = monitor.get_performance_summary()
        print(f"   • Monitoring duration: {summary.get('monitoring_duration', 0):.1f}s")
        
        if summary.get('gpu_stats') and torch.cuda.is_available():
            gpu = summary['gpu_stats']
            print(f"   • GPU utilization: {gpu.get('avg_utilization', 0):.1f}%")
            print(f"   • GPU memory used: {gpu.get('avg_memory_used', 0):.1f}GB")
        
        if summary.get('training_stats'):
            train = summary['training_stats']
            print(f"   • Training epochs: {train.get('total_epochs', 0)}")
        
        print("   ✅ Performance monitoring test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Performance monitoring test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_mechanisms():
    """Test GPU fallback mechanisms for advanced models."""
    print("\n🔄 Testing Fallback Mechanisms...")
    
    try:
        from utils.gpu_fallback import get_fallback_manager
        from models.advanced_models.saits_model import SAITSModel
        from models.advanced_models.brits_model import BRITSModel
        
        fallback = get_fallback_manager()
        
        # Test safe device selection
        safe_device = fallback.get_safe_device('cuda')
        print(f"   • Safe device: {safe_device}")
        
        # Test models with explicit CPU fallback
        print("   Testing CPU fallback...")
        saits_cpu = SAITSModel(
            n_features=4,
            sequence_len=16,
            epochs=1,
            batch_size=4,
            device='cpu'  # Force CPU
        )
        print(f"     • SAITS CPU device: {saits_cpu.device}")
        
        brits_cpu = BRITSModel(
            n_features=4,
            sequence_len=16,
            epochs=1,
            batch_size=4,
            device='cpu'  # Force CPU
        )
        print(f"     • BRITS CPU device: {brits_cpu.device}")
        
        # Get fallback summary
        summary = fallback.get_fallback_summary()
        print(f"   • Total fallbacks: {summary['total_fallbacks']}")
        print(f"   • GPU available: {summary['gpu_available']}")
        
        print("   ✅ Fallback mechanisms test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Fallback mechanisms test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_registry_integration():
    """Test integration with model registry."""
    print("\n📋 Testing Model Registry Integration...")
    
    try:
        from ml_core import MODEL_REGISTRY
        
        # Check if advanced models are in registry
        advanced_models = ['saits', 'brits']
        found_models = []
        
        for model_name in advanced_models:
            if model_name in MODEL_REGISTRY:
                found_models.append(model_name)
                model_config = MODEL_REGISTRY[model_name]
                print(f"   • {model_name}: {model_config.get('name', 'Unknown')}")
                print(f"     - Type: {model_config.get('type', 'Unknown')}")
                print(f"     - Available: {model_config.get('model_class') is not None}")
        
        print(f"   • Found {len(found_models)}/{len(advanced_models)} advanced models in registry")
        
        if found_models:
            print("   ✅ Model registry integration test passed")
            return True
        else:
            print("   ⚠️ No advanced models found in registry (may be expected)")
            return True
        
    except Exception as e:
        print(f"   ❌ Model registry integration test failed: {e}")
        return False

def main():
    """Run comprehensive advanced models GPU optimization tests."""
    print("🚀 ADVANCED MODELS GPU OPTIMIZATION TEST SUITE")
    print("=" * 70)
    
    test_results = {}
    
    # Run all tests
    test_results['advanced_models_availability'] = test_advanced_models_availability()
    test_results['gpu_device_management'] = test_gpu_device_management()
    test_results['saits_gpu_training'] = test_saits_gpu_training()
    test_results['brits_gpu_training'] = test_brits_gpu_training()
    test_results['performance_monitoring'] = test_performance_monitoring()
    test_results['fallback_mechanisms'] = test_fallback_mechanisms()
    test_results['model_registry_integration'] = test_model_registry_integration()
    
    # Print summary
    print("\n" + "=" * 70)
    print("📋 ADVANCED MODELS TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title():<35} {status}")
    
    print("-" * 70)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All advanced models tests passed! GPU optimization is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
