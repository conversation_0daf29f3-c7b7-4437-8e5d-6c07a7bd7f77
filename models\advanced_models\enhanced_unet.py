"""
Enhanced UNet Implementation using MONAI
True U-Net architecture with skip connections for well log imputation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from monai.networks.nets import UNet
from monai.networks.layers import Norm
from .base_model import BaseAdvancedModel
from typing import Dict, Any, Optional, List

class EnhancedUNet(BaseAdvancedModel):
    """
    Enhanced U-Net model using MONAI framework.
    True U-Net architecture with skip connections for spatial pattern recognition.
    """

    def __init__(self, n_features=4, sequence_len=64,
                 channels=(32, 64, 128, 256), strides=(2, 2, 2),
                 epochs=50, batch_size=32, learning_rate=1e-4, **kwargs):
        """
        Initialize Enhanced UNet model.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            channels: Channel sizes for each level of U-Net
            strides: Stride sizes for downsampling
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, **kwargs)

        self.channels = channels
        self.strides = strides
        self.learning_rate = learning_rate

        # Validate parameters
        self._validate_parameters()

        # Training components
        self.optimizer = None
        self.criterion = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if len(self.channels) != len(self.strides) + 1:
            raise ValueError(f"channels length ({len(self.channels)}) must be strides length + 1 ({len(self.strides) + 1})")

        if self.sequence_len < 32:
            print("⚠️ Warning: sequence_len < 32 may not be optimal for U-Net architecture")

        # Check if sequence length is compatible with strides
        min_size = self.sequence_len
        for stride in self.strides:
            min_size = min_size // stride
        if min_size < 4:
            print(f"⚠️ Warning: sequence_len {self.sequence_len} may be too small for given strides {self.strides}")

    def _initialize_model(self) -> None:
        """Initialize the MONAI UNet model."""
        try:
            # Create 2D U-Net (treating sequence as spatial dimension)
            self.model = UNet(
                spatial_dims=2,
                in_channels=1,  # Single channel input (will reshape data)
                out_channels=1,  # Single channel output
                channels=self.channels,
                strides=self.strides,
                num_res_units=2,  # Residual units per level
                norm=Norm.BATCH,
                dropout=0.1,
                bias=True
            ).to(self.device)

            # Initialize optimizer and loss function
            self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            self.criterion = nn.MSELoss()

            print(f"✅ Enhanced UNet initialized with channels {self.channels}")
            print(f"   Device: {self.device}")
            print(f"   Total parameters: {sum(p.numel() for p in self.model.parameters()):,}")

        except Exception as e:
            raise RuntimeError(f"Failed to initialize Enhanced UNet model: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Prepare data for U-Net training/prediction.

        Args:
            data: Input data tensor (batch, sequence, features)
            truth_data: Ground truth data (optional, for training)

        Returns:
            Dictionary with prepared tensors
        """
        # Reshape data for 2D U-Net: (batch, 1, sequence, features)
        if len(data.shape) == 3:
            data_reshaped = data.unsqueeze(1)  # Add channel dimension
        else:
            data_reshaped = data

        data_reshaped = data_reshaped.to(self.device)

        result = {'input': data_reshaped}

        if truth_data is not None:
            if len(truth_data.shape) == 3:
                truth_reshaped = truth_data.unsqueeze(1)
            else:
                truth_reshaped = truth_data
            truth_reshaped = truth_reshaped.to(self.device)
            result['target'] = truth_reshaped

        return result

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the Enhanced UNet model.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        if self.model is None:
            self._initialize_model()

        epochs = epochs or self.epochs
        print(f"Training Enhanced UNet for {epochs} epochs...")

        # Prepare data
        data_dict = self._prepare_data(train_data, truth_data)
        input_data = data_dict['input']
        target_data = data_dict['target']

        # Create missing value mask
        missing_mask = torch.isnan(input_data)
        input_data_filled = input_data.clone()
        input_data_filled[missing_mask] = 0.0  # Fill NaN with zeros for U-Net

        self.model.train()
        training_losses = []

        for epoch in range(epochs):
            self.optimizer.zero_grad()

            # Forward pass
            outputs = self.model(input_data_filled)

            # Calculate loss only on non-missing values
            valid_mask = ~missing_mask
            if valid_mask.sum() > 0:
                loss = self.criterion(outputs[valid_mask], target_data[valid_mask])
            else:
                loss = self.criterion(outputs, target_data)

            # Backward pass
            loss.backward()
            self.optimizer.step()

            training_losses.append(loss.item())

            if (epoch + 1) % 10 == 0:
                print(f"   Epoch {epoch + 1}/{epochs}, Loss: {loss.item():.6f}")

        self.is_fitted = True
        self.training_history = {'losses': training_losses}
        print("Enhanced UNet training completed!")

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using Enhanced UNet.

        Args:
            data: Input data with missing values

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        self.model.eval()

        with torch.no_grad():
            # Prepare data
            data_dict = self._prepare_data(data)
            input_data = data_dict['input']

            # Handle missing values
            missing_mask = torch.isnan(input_data)
            input_data_filled = input_data.clone()
            input_data_filled[missing_mask] = 0.0

            # Predict
            outputs = self.model(input_data_filled)

            # Combine original and predicted values
            result = input_data.clone()
            result[missing_mask] = outputs[missing_mask]

            # Remove channel dimension and return to CPU
            if result.shape[1] == 1:
                result = result.squeeze(1)

            return result.cpu()

    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics."""
        if self.model is None:
            self._initialize_model()

        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'channels': self.channels,
            'strides': self.strides,
            'complexity_score': 2  # Medium-high complexity
        }

    def get_feature_maps(self, data: torch.Tensor, layer_name: str = 'encoder') -> Optional[torch.Tensor]:
        """
        Extract feature maps from specified layer.

        Args:
            data: Input data tensor
            layer_name: Name of layer to extract features from

        Returns:
            Feature maps tensor or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting feature maps")
            return None

        # This would require hooks to extract intermediate features
        # For now, return None and implement in future versions
        print("ℹ️ Feature map extraction not yet implemented")
        return None
