#!/usr/bin/env python3
"""
Minimal test to verify the sequence creation fix for prediction mode.
This simulates the exact scenario that was failing.
"""

import numpy as np
import pandas as pd
import sys
import os

def test_prediction_scenario():
    """Test the exact prediction scenario that was failing."""
    print("🧪 Testing Prediction Scenario Fix")
    print("=" * 50)
    
    # Create test data similar to the actual well log data
    np.random.seed(42)
    wells = ['B-G-6', 'B-G-10', 'B-L-1']
    all_data = []
    
    for well in wells:
        n_points = 150
        
        # Create synthetic well log data
        md_values = np.linspace(1000, 1150, n_points)
        gr = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, n_points)) + np.random.normal(0, 5, n_points)
        rhob = 2.3 + 0.3 * np.cos(np.linspace(0, 3*np.pi, n_points)) + np.random.normal(0, 0.05, n_points)
        nphi = 0.15 + 0.1 * np.sin(np.linspace(0, 5*np.pi, n_points)) + np.random.normal(0, 0.02, n_points)
        pwave = 8000 + 2000 * (rhob - 2.3) / 0.3 - 5000 * nphi + np.random.normal(0, 200, n_points)
        
        # Add some missing values in P-WAVE (30% missing)
        missing_indices = np.random.choice(n_points, size=int(0.3 * n_points), replace=False)
        pwave[missing_indices] = np.nan
        
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': md_values,
            'GR': gr,
            'RHOB': rhob,
            'NPHI': nphi,
            'P-WAVE': pwave
        })
        
        all_data.append(well_data)
    
    df = pd.concat(all_data, ignore_index=True)
    print(f"✅ Created test data: {df.shape}")
    print(f"   Wells: {df['WELL'].unique()}")
    print(f"   P-WAVE missing: {df['P-WAVE'].isna().sum()} / {len(df)} ({df['P-WAVE'].isna().mean()*100:.1f}%)")
    
    # Simulate the exact prediction preparation that was failing
    feature_cols = ['GR', 'RHOB', 'NPHI']
    target_col = 'P-WAVE'
    all_features = feature_cols + [target_col]
    
    # Step 1: Forward-fill and back-fill feature columns (like prepare_prediction_data does)
    prediction_input_df = df.copy()
    prediction_input_df[feature_cols] = prediction_input_df[feature_cols].ffill().bfill()
    
    # Step 2: Mask the entire target column (like prepare_prediction_data does)
    prediction_input_df[target_col] = np.nan
    
    print(f"\n📊 Prediction data prepared:")
    print(f"   GR missing: {prediction_input_df['GR'].isna().sum()}")
    print(f"   RHOB missing: {prediction_input_df['RHOB'].isna().sum()}")
    print(f"   NPHI missing: {prediction_input_df['NPHI'].isna().sum()}")
    print(f"   P-WAVE missing: {prediction_input_df['P-WAVE'].isna().sum()} / {len(prediction_input_df)} ({prediction_input_df['P-WAVE'].isna().mean()*100:.1f}%)")
    
    # Step 3: Test sequence creation with enhanced preprocessing (this was failing before)
    try:
        from enhanced_preprocessing import EnhancedLogPreprocessor
        
        print(f"\n🔧 Testing enhanced sequence creation...")
        preprocessor = EnhancedLogPreprocessor(sequence_len=32, sequence_stride=1)
        sequences, metadata = preprocessor.create_sequences_enhanced(prediction_input_df, 'WELL', all_features)
        
        print(f"✅ Enhanced sequences created: {sequences.shape}")
        print(f"✅ Metadata entries: {len(metadata)}")
        
        if sequences.shape[0] > 0:
            print(f"✅ SUCCESS: Enhanced preprocessing created {sequences.shape[0]} sequences!")
            enhanced_success = True
        else:
            print(f"❌ FAILED: Enhanced preprocessing created 0 sequences")
            enhanced_success = False
            
    except Exception as e:
        print(f"❌ Enhanced preprocessing error: {e}")
        import traceback
        traceback.print_exc()
        enhanced_success = False
    
    # Step 4: Test sequence creation with standard preprocessing
    try:
        from data_handler import create_sequences
        
        print(f"\n🔧 Testing standard sequence creation...")
        sequences_std, metadata_std = create_sequences(prediction_input_df, 'WELL', all_features, 
                                                      sequence_len=32, use_enhanced=False)
        
        print(f"✅ Standard sequences created: {sequences_std.shape}")
        print(f"✅ Metadata entries: {len(metadata_std)}")
        
        if sequences_std.shape[0] > 0:
            print(f"✅ SUCCESS: Standard preprocessing created {sequences_std.shape[0]} sequences!")
            standard_success = True
        else:
            print(f"❌ FAILED: Standard preprocessing created 0 sequences")
            standard_success = False
            
    except Exception as e:
        print(f"❌ Standard preprocessing error: {e}")
        import traceback
        traceback.print_exc()
        standard_success = False
    
    # Step 5: Test the wrapper function that's actually called in ml_core.py
    try:
        from data_handler import create_sequences
        
        print(f"\n🔧 Testing wrapper function (use_enhanced=True)...")
        sequences_wrapper, metadata_wrapper = create_sequences(prediction_input_df, 'WELL', all_features, 
                                                              sequence_len=32, use_enhanced=True)
        
        print(f"✅ Wrapper sequences created: {sequences_wrapper.shape}")
        print(f"✅ Metadata entries: {len(metadata_wrapper)}")
        
        if sequences_wrapper.shape[0] > 0:
            print(f"✅ SUCCESS: Wrapper function created {sequences_wrapper.shape[0]} sequences!")
            wrapper_success = True
        else:
            print(f"❌ FAILED: Wrapper function created 0 sequences")
            wrapper_success = False
            
    except Exception as e:
        print(f"❌ Wrapper function error: {e}")
        import traceback
        traceback.print_exc()
        wrapper_success = False
    
    # Summary
    print(f"\n📋 Test Results Summary:")
    print(f"   Enhanced preprocessing: {'✅ PASSED' if enhanced_success else '❌ FAILED'}")
    print(f"   Standard preprocessing: {'✅ PASSED' if standard_success else '❌ FAILED'}")
    print(f"   Wrapper function: {'✅ PASSED' if wrapper_success else '❌ FAILED'}")
    
    overall_success = enhanced_success and standard_success and wrapper_success
    
    if overall_success:
        print(f"\n🎉 ALL TESTS PASSED! The sequence creation fix is working correctly.")
        print(f"   The SAITS model should now be able to create sequences for prediction.")
    else:
        print(f"\n❌ SOME TESTS FAILED! The fix needs more work.")
    
    return overall_success

if __name__ == "__main__":
    success = test_prediction_scenario()
    sys.exit(0 if success else 1)
