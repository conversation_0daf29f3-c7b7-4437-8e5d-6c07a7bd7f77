#!/usr/bin/env python3
"""
Test script to reproduce the exact SAITS error from main application
"""

import numpy as np
import pandas as pd
import torch
from ml_core import MODEL_REGISTRY, impute_logs_deep

def test_saits_main_scenario():
    """Test SAITS with the exact scenario from main application"""
    print("🔍 Testing SAITS with main application scenario...")
    
    # Create realistic well log data that mimics the LAS files
    np.random.seed(42)
    n_points_per_well = 500
    
    # Create data that's more similar to actual LAS files
    df = pd.DataFrame({
        'WELL': ['WELL_A'] * n_points_per_well + ['WELL_B'] * n_points_per_well + ['WELL_C'] * n_points_per_well,
        'MD': np.concatenate([
            np.linspace(1000, 2000, n_points_per_well),
            np.linspace(1500, 2500, n_points_per_well), 
            np.linspace(2000, 3000, n_points_per_well)
        ]),
        'GR': np.concatenate([
            np.random.normal(60, 15, n_points_per_well),
            np.random.normal(45, 12, n_points_per_well),
            np.random.normal(70, 20, n_points_per_well)
        ]),
        'NPHI': np.concatenate([
            np.random.normal(0.15, 0.05, n_points_per_well),
            np.random.normal(0.25, 0.08, n_points_per_well),
            np.random.normal(0.20, 0.06, n_points_per_well)
        ]),
        'RHOB': np.concatenate([
            np.random.normal(2.4, 0.2, n_points_per_well),
            np.random.normal(2.2, 0.15, n_points_per_well),
            np.random.normal(2.5, 0.25, n_points_per_well)
        ]),
        'P-WAVE': np.concatenate([
            np.random.normal(180, 30, n_points_per_well),
            np.random.normal(220, 40, n_points_per_well),
            np.random.normal(160, 25, n_points_per_well)
        ])
    })
    
    # Add realistic missing patterns like in actual LAS files
    for well in df['WELL'].unique():
        well_mask = df['WELL'] == well
        well_indices = df[well_mask].index
        
        # Add several missing sections
        for i in range(5):
            start_idx = np.random.choice(well_indices[20:-20])
            end_idx = start_idx + np.random.randint(10, 30)
            df.loc[start_idx:end_idx, 'P-WAVE'] = np.nan
    
    # Use the exact configuration that main.py would use
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'P-WAVE'
    
    print(f"Data shape: {df.shape}")
    print(f"Wells: {df['WELL'].unique()}")
    print(f"Features: {feature_cols}")
    print(f"Target: {target_col}")
    print(f"Missing values in target: {df[target_col].isna().sum()}")
    
    # Get SAITS configuration from MODEL_REGISTRY (exactly like main.py)
    saits_config = MODEL_REGISTRY['saits']
    print(f"SAITS config: {saits_config['name']}")
    
    # Use default hyperparameters (like configure_hyperparameters() would return)
    hparams = {
        'n_features': len(feature_cols) + 1,  # Including target
        'sequence_len': 64,
        'epochs': 50,
        'batch_size': 32,
        'learning_rate': 1e-3,
        'n_layers': 2,
        'd_model': 256,
        'n_heads': 4,
        'dropout': 0.1
    }
    
    print(f"Hyperparameters: {hparams}")
    
    try:
        # Call impute_logs_deep exactly like main.py does
        print("\n🚀 Running impute_logs_deep...")
        res_df, mres = impute_logs_deep(df, feature_cols, target_col, saits_config, hparams)
        
        if mres and res_df is not None:
            print("✅ SAITS completed successfully!")
            print(f"   Result shape: {res_df.shape}")
            print(f"   Evaluations: {len(mres['evaluations'])}")
            return True
        else:
            print("❌ SAITS failed to produce results")
            return False
            
    except Exception as e:
        print(f"❌ SAITS failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_saits_main_scenario()
    if success:
        print("\n✅ SAITS test with main scenario passed!")
    else:
        print("\n❌ SAITS test with main scenario failed!")
