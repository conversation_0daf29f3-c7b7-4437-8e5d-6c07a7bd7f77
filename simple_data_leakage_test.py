"""
Simple test to verify the core data leakage fix functionality.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the current directory to the path to import ml_core
sys.path.append(os.getcwd())

def test_prepare_prediction_data_simple():
    """Simple test of the prepare_prediction_data function."""
    print("Testing prepare_prediction_data function...")
    
    # Import the function
    try:
        from ml_core import prepare_prediction_data
    except ImportError as e:
        print(f"❌ Could not import prepare_prediction_data: {e}")
        return False
    
    # Create simple test data
    data = {
        'feature1': [1, 2, 3, 4, 5],
        'feature2': [10, 20, 30, 40, 50],
        'target': [100, np.nan, 300, np.nan, 500]
    }
    df = pd.DataFrame(data)
    
    feature_cols = ['feature1', 'feature2']
    target_col = 'target'
    
    # Test the function
    try:
        result_df = prepare_prediction_data(df, feature_cols, target_col)
        
        # Check that target NaNs are preserved
        original_nan_count = df[target_col].isna().sum()
        result_nan_count = result_df[target_col].isna().sum()
        
        if original_nan_count == result_nan_count:
            print(f"✅ Target NaN count preserved: {original_nan_count}")
        else:
            print(f"❌ Target NaN count changed: {original_nan_count} -> {result_nan_count}")
            return False
        
        # Check that feature columns have no NaNs
        for col in feature_cols:
            if result_df[col].isna().any():
                print(f"❌ Feature column {col} still has NaNs")
                return False
        
        print("✅ prepare_prediction_data test passed")
        return True
        
    except Exception as e:
        print(f"❌ Error in prepare_prediction_data: {e}")
        return False

def test_data_leakage_prevention():
    """Test that the key data leakage prevention logic works."""
    print("Testing data leakage prevention logic...")
    
    # Create sample data
    original_data = pd.Series([1, 2, np.nan, 4, np.nan, 6])
    predicted_data = pd.Series([1.1, 2.1, 3.1, 4.1, 5.1, 6.1])
    
    # Test the correct imputation logic (as implemented in the fix)
    # The '_imputed' column should show original data with NaNs filled by predictions
    imputed_data = original_data.fillna(predicted_data)
    
    # Verify that original non-NaN values are preserved
    original_mask = original_data.notna()
    if (imputed_data[original_mask] == original_data[original_mask]).all():
        print("✅ Original values preserved in imputed data")
    else:
        print("❌ Original values were modified in imputed data")
        return False
    
    # Verify that NaN positions were filled with predictions
    nan_mask = original_data.isna()
    if (imputed_data[nan_mask] == predicted_data[nan_mask]).all():
        print("✅ NaN positions filled with predictions")
    else:
        print("❌ NaN positions not properly filled")
        return False
    
    print("✅ Data leakage prevention test passed")
    return True

def test_validation_masking_logic():
    """Test the validation masking logic."""
    print("Testing validation masking logic...")
    
    # Simulate the validation masking logic from the fix
    # Create sample data where some values are artificially missing
    truth_values = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
    missing_mask = np.array([False, True, False, True, False, True, False, False, True, False])
    
    # Create missing data (NaN where mask is True)
    missing_values = truth_values.copy().astype(float)
    missing_values[missing_mask] = np.nan
    
    # Simulate model predictions
    predicted_values = truth_values + 0.1  # Small prediction error
    
    # Apply the validation masking logic (only evaluate on missing positions)
    eval_mask = np.isnan(missing_values)
    y_true_eval = truth_values[eval_mask]
    y_pred_eval = predicted_values[eval_mask]
    
    # Verify that we only evaluate on the missing positions
    expected_count = missing_mask.sum()
    actual_count = len(y_true_eval)
    
    if expected_count == actual_count:
        print(f"✅ Validation masking correct: evaluated on {actual_count} missing values")
    else:
        print(f"❌ Validation masking incorrect: expected {expected_count}, got {actual_count}")
        return False
    
    print("✅ Validation masking logic test passed")
    return True

def run_simple_tests():
    """Run all simple tests."""
    print("=" * 50)
    print("RUNNING SIMPLE DATA LEAKAGE FIX TESTS")
    print("=" * 50)
    
    tests = [
        test_prepare_prediction_data_simple,
        test_data_leakage_prevention,
        test_validation_masking_logic
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All simple data leakage fix tests passed!")
        return True
    else:
        print("⚠️ Some tests failed")
        return False

if __name__ == "__main__":
    run_simple_tests()
