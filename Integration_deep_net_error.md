### Analysis of the Core Problems

#### 1. The "Data Leakage Illusion" in the `_imputed` Column

In both `impute_logs` (for shallow models) and `impute_logs_deep`, the final "imputed" column is created with this line:

```python
# From ml_core.py
res[imp_col] = res[target_col].fillna(full_pred) # or a variation of this
```

**What this does:**
*   It takes the original target log (`res[target_col]`).
*   Wherever a value is **not** NaN, it keeps the original value.
*   It only uses the model's prediction (`full_pred`) to fill in the cells that were **originally NaN**.

When you then compare this `_imputed` column against the original `target_col`, you are mostly comparing the original data against itself. The metrics will be perfect (or near-perfect) because the only differences are in the NaN gaps. This is why `create_separate_comparison_plots` shows a perfect match for "Original vs Imputed".

**This is not necessarily a bug in the code's intent, but a misunderstanding of what is being displayed.** The true, raw performance of the model is in the `_pred` column, which is generated *before* this `fillna` step.

#### 2. The Critical Bug: Scrambled Predictions in the `_pred` Column

This is the real reason the models appear to fail. The `impute_logs_deep` function correctly prepares sequences, trains the model, and generates predicted sequences. However, the logic to put these predictions back into the main dataframe is flawed.

Look at this section in `impute_logs_deep` in `ml_core.py`:

```python
# ml_core.py -> impute_logs_deep (FLAWED LOGIC)

# --- Reconstruct from sequences ---
# This part is complex; using a simplified approach for now
# A more robust solution would track indices from sequence creation

# Create a temporary dataframe for imputed values
imputed_df_scaled = df_scaled.copy()
imputed_values_flat = imputed_sequences.reshape(-1, len(all_features))

# This is an approximation, might have overlaps
num_to_fill = min(len(imputed_df_scaled), len(imputed_values_flat))
# 💥 CRITICAL BUG HERE 💥
imputed_df_scaled.iloc[:num_to_fill, imputed_df_scaled.columns.get_indexer(all_features)] = imputed_values_flat[:num_to_fill]

# ... inverse transform ...
res_df[pred_col] = imputed_df_scaled[target_col]
```

The sequences are created from various wells and at different, often non-contiguous, depth intervals. This code flattens all those predicted sequences and simply pastes them onto the top of the dataframe using `iloc`. This completely destroys the relationship between the prediction and its original well and depth, leading to a scrambled `_pred` column and terrible R² scores.

#### 3. Sub-optimal Model Implementation in `simple_autoencoder.py`

The `SimpleAutoencoder.predict` method has a slight issue that contributes to the confusion.

```python
# simple_autoencoder.py -> predict (SUB-OPTIMAL)
def predict(self, data):
    # ...
    reconstructed = self.model(data_flat)
    # ...
    # It returns a mix of original and predicted values
    imputed_data = data.clone()
    imputed_data[missing_mask] = reconstructed[missing_mask]
    return imputed_data
```
The training process (`fit`) learns to reconstruct the *entire* input sequence. However, the `predict` function was designed to only return the values for the missing parts. For a full "prediction" curve, we need the entire reconstructed output from the model.

---

### The Solution: A Step-by-Step Fix

We need to refactor the workflow to correctly track and re-assemble the sequence predictions.

#### Step 1: Modify `create_sequences` to Track Metadata

First, we need `create_sequences` to tell us where each sequence came from. We will modify it to return a list of metadata dictionaries. Your `enhanced_preprocessing.py` file already contains a superior version of this logic, so we'll adapt the standard one in `data_handler.py`.

**File: `data_handler.py`**

Replace the existing `create_sequences` function with this improved version:

```python
def create_sequences(df, well_col, feature_cols, sequence_len=64, step=1, use_enhanced=False):
    """Creates sequences and metadata from well data for deep learning models."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced sequence creation with valid interval detection...")
        sequences, metadata = enhanced_create_sequences(df, well_col, feature_cols, sequence_len, step)
        return np.array(sequences), metadata

    all_sequences = []
    metadata = []
    
    # Ensure dataframe is sorted by well and depth for correct indexing
    df_sorted = df.sort_values(by=[well_col, 'MD']).reset_index()

    for well in df_sorted[well_col].unique():
        well_df = df_sorted[df_sorted[well_col] == well]
        
        # Identify continuous intervals of data (no NaNs in features)
        is_valid = well_df[feature_cols].notna().all(axis=1)
        valid_intervals = np.where(np.diff(np.concatenate(([False], is_valid, [False]))))[0].reshape(-1, 2)
        
        for start, end in valid_intervals:
            interval_len = end - start
            if interval_len < sequence_len:
                continue

            # Extract data for this valid interval
            interval_data = well_df.iloc[start:end]
            data_values = interval_data[feature_cols].values
            
            num_sequences_in_interval = (len(data_values) - sequence_len) // step + 1
            for i in range(num_sequences_in_interval):
                seq_start = i * step
                seq_end = seq_start + sequence_len
                
                # Original dataframe indices for this sequence
                original_indices = interval_data.index[seq_start:seq_end].tolist()

                all_sequences.append(data_values[seq_start:seq_end])
                metadata.append({
                    'well': well,
                    'original_indices': original_indices
                })

    if not all_sequences:
        print("Warning: No valid sequences could be created. Check data quality and sequence length.")
        return np.array([]), []

    print(f"Created {len(all_sequences)} sequences of length {sequence_len}.")
    return np.array(all_sequences), metadata
```

#### Step 2: Fix the Model's `predict` Method

The model should return its full reconstruction, not a mix of original and predicted values.

**File: `simple_autoencoder.py`**

Modify the `predict` function in `SimpleAutoencoder` (and `SimpleUNet` if you use it).

```python
# In class SimpleAutoencoder
def predict(self, data):
    """
    Predict/reconstruct the full input.

    Args:
        data: Input data (can have missing values)

    Returns:
        Fully reconstructed data from the autoencoder.
    """
    # Input validation
    if not isinstance(data, torch.Tensor):
        raise TypeError("data must be torch.Tensor")

    if len(data.shape) != 3:
        raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {data.shape}")

    if data.shape[1] != self.sequence_len or data.shape[2] != self.n_features:
        raise ValueError(f"Data shape {data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")

    self.model.eval()

    with torch.no_grad():
        # Replace NaN with zeros for model input, as done in training
        data_clean = torch.nan_to_num(data, nan=0.0)

        # Flatten the input data
        data_flat = data_clean.view(data_clean.size(0), -1)

        # Get reconstructed data
        reconstructed_flat = self.model(data_flat)

        # Reshape back to original 3D shape
        reconstructed_tensor = reconstructed_flat.view(data.shape)

    return reconstructed_tensor
```

#### Step 3: Rewrite the Prediction and Post-processing Logic

This is the most important fix. We will use the metadata from Step 1 to correctly re-assemble the predictions.

**File: `ml_core.py`**

Replace the entire `impute_logs_deep` function with this corrected version:

```python
def impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing=True):
    """Main imputation routine for a single deep learning model."""
    print(f"--- Running Deep Learning Model: {model_config['name']} ---")

    # 1. Data Preparation
    all_features = feature_cols + [target_col]
    
    # --- TRAIN/VALIDATION SPLIT ---
    wells = df['WELL'].unique()
    train_wells, val_wells = train_test_split(wells, test_size=0.2, random_state=42)
    train_df = df[df['WELL'].isin(train_wells)]
    val_df = df[df['WELL'].isin(val_wells)]
    print(f"Training wells: {len(train_wells)}, Validation wells: {len(val_wells)}")

    # Normalize training data and get scalers
    train_df_scaled, scalers = normalize_data(train_df, all_features, use_enhanced=use_enhanced_preprocessing)

    # Create sequences for training
    train_sequences_true, _ = create_sequences(train_df_scaled, 'WELL', all_features,
                                               sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    if train_sequences_true.shape[0] == 0:
        print("❌ ERROR: No training sequences were created. Cannot proceed.")
        return df, None
        
    train_sequences_missing = introduce_missingness(train_sequences_true, target_col_name=target_col, 
                                                    feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)

    # Create sequences for validation
    val_df_scaled, _ = normalize_data(val_df, all_features, use_enhanced=use_enhanced_preprocessing, scalers=scalers)
    val_sequences_true, _ = create_sequences(val_df_scaled, 'WELL', all_features,
                                             sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    val_sequences_missing = introduce_missingness(val_sequences_true, target_col_name=target_col,
                                                  feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)

    # Convert to PyTorch tensors
    train_tensor = torch.from_numpy(train_sequences_missing.astype(np.float32))
    truth_tensor = torch.from_numpy(train_sequences_true.astype(np.float32))
    val_train_tensor = torch.from_numpy(val_sequences_missing.astype(np.float32))
    val_truth_tensor = torch.from_numpy(val_sequences_true.astype(np.float32))

    # 2. Model Initialization
    hparams['n_features'] = len(all_features)
    if not DEEP_MODELS_AVAILABLE or model_config['model_class'] is None:
        print(f"Deep learning model {model_config['name']} not available. Skipping.")
        return df, None
    model = model_config['model_class'](**hparams)

    # 3. Training
    print("Training phase...")
    model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'], batch_size=hparams['batch_size'])

    # 4. Evaluation on Validation Set
    print("Evaluation phase...")
    mae, r2, rmse, composite_score = -1, -1, -1, -1
    if val_train_tensor.shape[0] > 0:
        imputed_val_tensor = model.predict(val_train_tensor)
        target_idx = all_features.index(target_col)
        y_pred_val = imputed_val_tensor[:, :, target_idx].flatten().detach().cpu().numpy()
        y_true_val = val_truth_tensor[:, :, target_idx].flatten().detach().cpu().numpy()

        mae = mean_absolute_error(y_true_val, y_pred_val)
        r2 = r2_score(y_true_val, y_pred_val)
        rmse = np.sqrt(mean_squared_error(y_true_val, y_pred_val))
        r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
        composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
        print(f"Validation Metrics: MAE={mae:.4f}, R2={r2:.4f}, RMSE={rmse:.4f}, Composite={composite_score:.4f}")
    else:
        print("Warning: No validation sequences to evaluate.")

    # 5. Imputation (Prediction on all data)
    print("Prediction phase (full dataset)...")
    
    # Prepare the full dataset for prediction
    df_scaled, full_scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing)
    
    # We need the original data with NaNs to feed into the model
    sequences_to_predict, metadata = create_sequences(df_scaled, 'WELL', all_features,
                                                      sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    if sequences_to_predict.shape[0] == 0:
        print("❌ ERROR: No sequences created from the full dataset for prediction.")
        return df, None

    # Predict on the sequences
    prediction_tensor = torch.from_numpy(sequences_to_predict.astype(np.float32))
    imputed_full_tensor = model.predict(prediction_tensor)
    imputed_sequences = imputed_full_tensor.detach().cpu().numpy()

    # 6. Post-processing & Re-assembly
    print("Post-processing and re-assembling results...")
    
    # Create placeholder arrays to aggregate predictions
    # We use a temporary dataframe to hold the sum of predictions and a count
    pred_sum_df = pd.DataFrame(0.0, index=df.index, columns=all_features)
    pred_count_df = pd.DataFrame(0, index=df.index, columns=all_features)

    # Loop through each predicted sequence and its metadata
    for i, seq_meta in enumerate(metadata):
        original_indices = seq_meta['original_indices']
        predicted_sequence = imputed_sequences[i] # Shape: (sequence_len, n_features)
        
        # Add the prediction to the sum and increment the count
        pred_sum_df.loc[original_indices, all_features] += predicted_sequence
        pred_count_df.loc[original_indices, all_features] += 1

    # Calculate the average prediction for each point
    # Avoid division by zero where no prediction was made
    avg_pred_df_scaled = pred_sum_df.divide(pred_count_df).fillna(0)

    # Create the final imputed dataframe (scaled)
    imputed_df_scaled = df_scaled.copy()
    # Update only the parts where we have a prediction
    update_mask = pred_count_df > 0
    imputed_df_scaled[update_mask] = avg_pred_df_scaled[update_mask]

    # Inverse transform to get back to original scale
    imputed_df_unscaled = imputed_df_scaled.copy()
    for col in all_features:
        if col in full_scalers and full_scalers[col] is not None:
            # We must reshape for the scaler
            valid_mask = imputed_df_unscaled[col].notna()
            col_data = imputed_df_unscaled.loc[valid_mask, [col]].values
            if col_data.shape[0] > 0:
                imputed_df_unscaled.loc[valid_mask, col] = full_scalers[col].inverse_transform(col_data)

    # Final assignment to result dataframe
    res_df = df.copy()
    pred_col = f'{target_col}_pred'
    imp_col = f'{target_col}_imputed'
    err_col = f'{target_col}_error'
    
    res_df[pred_col] = imputed_df_unscaled[target_col]
    res_df[imp_col] = res_df[target_col].fillna(res_df[pred_col])
    
    mask_orig = res_df[target_col].notna() & res_df[pred_col].notna()
    res_df.loc[mask_orig, err_col] = np.abs(res_df.loc[mask_orig, target_col] - res_df.loc[mask_orig, pred_col])

    print(f"✅ {model_config['name']} imputation complete.")

    eval_results = {
        'mae': mae, 'r2': r2, 'rmse': rmse,
        'model_name': model_config['name'], 'composite_score': composite_score
    }

    return res_df, {
        'target': target_col, 'evaluations': [eval_results],
        'best_model_name': model_config['name'], 'trained_models': {model_config['name']: model}
    }
```

### Explanation of the Fixes

1.  **`create_sequences`:** Now returns `metadata` containing the original DataFrame indices for each sequence. This is the crucial link we were missing.
2.  **`SimpleAutoencoder.predict`:** Now returns the model's full, raw reconstruction. This provides the `_pred` curve we want to analyze.
3.  **`impute_logs_deep` (The Major Fix):**
    *   It now gets `metadata` along with the sequences to predict.
    *   Instead of the flawed `iloc` pasting, it initializes two empty DataFrames: `pred_sum_df` and `pred_count_df`.
    *   It iterates through each predicted sequence. Using the `metadata`, it adds the prediction to the correct locations (by original index) in `pred_sum_df` and increments the counter in `pred_count_df`.
    *   This correctly handles the **overlapping sequences** by summing their contributions.
    *   After the loop, it computes the average prediction by dividing `pred_sum_df` by `pred_count_df`.
    *   This averaged, correctly-placed prediction is then inverse-scaled and used to create the final `_pred` column.

With these changes, the `_pred` column will now contain a coherent, depth-correct prediction from your deep learning models, and your evaluation metrics and plots for "Original vs Predicted" will accurately reflect the model's performance. The "Original vs Imputed" plot will still look very good, but you will now understand that it's not the primary measure of model quality.