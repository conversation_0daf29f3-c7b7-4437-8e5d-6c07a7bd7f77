# Test Files Archive

This directory contains test files that were moved from the root directory to keep the project structure clean and organized.

## Contents

### Test Files Moved (Date: 2025-07-06)

**Model Testing:**
- `test_base_model.py` - Base model functionality tests
- `test_enhanced_unet.py` - Enhanced U-Net model tests
- `test_mrnn_model.py` - MRNN model tests
- `test_transformer_model.py` - Transformer model tests
- `test_saits_debug.py` - SAITS model debugging tests
- `test_saits_difficult.py` - SAITS model complex scenario tests
- `test_saits_main_scenario.py` - SAITS model main scenario tests
- `test_full_saits.py` - Complete SAITS model tests

**Integration Testing:**
- `test_integration_phase2.py` - Phase 2 integration tests
- `test_phase2_models.py` - Phase 2 model tests
- `test_phase3_compatibility.py` - Phase 3 compatibility tests
- `test_phase4_integration.py` - Phase 4 integration tests

**System Testing:**
- `test_backward_compatibility.py` - Backward compatibility tests
- `test_data_pipeline.py` - Data pipeline tests
- `test_dependencies.py` - Dependency tests
- `test_enhanced_registry.py` - Enhanced registry tests
- `test_benchmarking.py` - Performance benchmarking tests

**Test Data:**
- `benchmark_results_test.csv` - Benchmark test results data
- `catboost_info/` - CatBoost model training logs and temporary files

## Usage

These test files can be run individually or as part of a test suite. To run tests from this archive:

```bash
# Run individual test
python archives/test_files/test_base_model.py

# Run all tests in archive (if using pytest)
pytest archives/test_files/
```

## Notes

- All test files maintain their original functionality
- Test files were moved to improve project organization
- The main `tests/` directory still contains active performance benchmarking tests
- These archived tests represent various development phases and can be used for regression testing

## Archive Structure

```
archives/
├── test_files/          # This directory - archived test files
└── second_stage/        # Previous archive of second stage development files
```
