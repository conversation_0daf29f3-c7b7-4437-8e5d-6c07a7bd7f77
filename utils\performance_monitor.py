"""
Performance Monitoring Utilities for ML Log Prediction
Provides GPU utilization monitoring and performance benchmarking capabilities.
"""

import time
import psutil
import torch
import numpy as np
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import threading
import queue

class PerformanceMonitor:
    """
    Comprehensive performance monitoring for ML training and inference.
    Tracks GPU utilization, memory usage, and training metrics.
    """
    
    def __init__(self, enable_gpu_monitoring=True, monitoring_interval=1.0):
        """
        Initialize performance monitor.
        
        Args:
            enable_gpu_monitoring: Whether to monitor GPU metrics
            monitoring_interval: Interval between monitoring samples (seconds)
        """
        self.enable_gpu_monitoring = enable_gpu_monitoring and torch.cuda.is_available()
        self.monitoring_interval = monitoring_interval
        self.monitoring_active = False
        self.monitoring_thread = None
        self.metrics_queue = queue.Queue()
        
        # Performance metrics storage
        self.metrics_history = {
            'timestamps': [],
            'cpu_percent': [],
            'memory_percent': [],
            'gpu_utilization': [],
            'gpu_memory_used': [],
            'gpu_memory_total': [],
            'gpu_temperature': []
        }
        
        # Training metrics
        self.training_metrics = {
            'epoch_times': [],
            'batch_times': [],
            'loss_values': [],
            'learning_rates': []
        }
        
        print(f"🔍 Performance Monitor initialized")
        if self.enable_gpu_monitoring:
            print(f"   • GPU monitoring enabled")
        print(f"   • Monitoring interval: {monitoring_interval}s")
    
    def start_monitoring(self):
        """Start background monitoring of system resources."""
        if self.monitoring_active:
            print("⚠️ Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        print("📊 Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        print("📊 Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop."""
        while self.monitoring_active:
            try:
                timestamp = time.time()
                
                # CPU and system memory
                cpu_percent = psutil.cpu_percent()
                memory_info = psutil.virtual_memory()
                memory_percent = memory_info.percent
                
                # GPU metrics
                gpu_util = 0
                gpu_memory_used = 0
                gpu_memory_total = 0
                gpu_temp = 0
                
                if self.enable_gpu_monitoring:
                    try:
                        # GPU utilization (approximation using memory usage)
                        gpu_memory_used = torch.cuda.memory_allocated() / 1e9  # GB
                        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1e9  # GB
                        gpu_util = (gpu_memory_used / gpu_memory_total) * 100 if gpu_memory_total > 0 else 0
                        
                        # GPU temperature (if available)
                        try:
                            import pynvml
                            pynvml.nvmlInit()
                            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                            gpu_temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                        except:
                            gpu_temp = 0  # Temperature monitoring not available
                    except Exception as e:
                        pass  # GPU monitoring failed, continue with zeros
                
                # Store metrics
                metrics = {
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'gpu_utilization': gpu_util,
                    'gpu_memory_used': gpu_memory_used,
                    'gpu_memory_total': gpu_memory_total,
                    'gpu_temperature': gpu_temp
                }
                
                # Add to queue (non-blocking)
                try:
                    self.metrics_queue.put_nowait(metrics)
                except queue.Full:
                    pass  # Queue full, skip this sample
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(self.monitoring_interval)
    
    def collect_metrics(self):
        """Collect all queued metrics into history."""
        while not self.metrics_queue.empty():
            try:
                metrics = self.metrics_queue.get_nowait()
                for key, value in metrics.items():
                    if key in self.metrics_history:
                        self.metrics_history[key].append(value)
            except queue.Empty:
                break
    
    @contextmanager
    def monitor_training_epoch(self):
        """Context manager for monitoring a training epoch."""
        start_time = time.time()
        start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        yield
        
        end_time = time.time()
        end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        epoch_time = end_time - start_time
        memory_delta = (end_memory - start_memory) / 1e6  # MB
        
        self.training_metrics['epoch_times'].append(epoch_time)
        
        print(f"   ⏱️ Epoch time: {epoch_time:.2f}s, Memory Δ: {memory_delta:+.1f}MB")
    
    @contextmanager
    def monitor_batch(self):
        """Context manager for monitoring a training batch."""
        start_time = time.time()
        
        yield
        
        end_time = time.time()
        batch_time = end_time - start_time
        self.training_metrics['batch_times'].append(batch_time)
    
    def log_training_metrics(self, loss=None, learning_rate=None):
        """Log training metrics."""
        if loss is not None:
            self.training_metrics['loss_values'].append(loss)
        if learning_rate is not None:
            self.training_metrics['learning_rates'].append(learning_rate)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        self.collect_metrics()
        
        summary = {
            'monitoring_duration': 0,
            'cpu_stats': {},
            'memory_stats': {},
            'gpu_stats': {},
            'training_stats': {}
        }
        
        if self.metrics_history['timestamps']:
            duration = max(self.metrics_history['timestamps']) - min(self.metrics_history['timestamps'])
            summary['monitoring_duration'] = duration
            
            # CPU statistics
            if self.metrics_history['cpu_percent']:
                summary['cpu_stats'] = {
                    'avg_utilization': np.mean(self.metrics_history['cpu_percent']),
                    'max_utilization': np.max(self.metrics_history['cpu_percent']),
                    'min_utilization': np.min(self.metrics_history['cpu_percent'])
                }
            
            # Memory statistics
            if self.metrics_history['memory_percent']:
                summary['memory_stats'] = {
                    'avg_usage': np.mean(self.metrics_history['memory_percent']),
                    'max_usage': np.max(self.metrics_history['memory_percent']),
                    'min_usage': np.min(self.metrics_history['memory_percent'])
                }
            
            # GPU statistics
            if self.enable_gpu_monitoring and self.metrics_history['gpu_utilization']:
                summary['gpu_stats'] = {
                    'avg_utilization': np.mean(self.metrics_history['gpu_utilization']),
                    'max_utilization': np.max(self.metrics_history['gpu_utilization']),
                    'avg_memory_used': np.mean(self.metrics_history['gpu_memory_used']),
                    'max_memory_used': np.max(self.metrics_history['gpu_memory_used']),
                    'memory_total': self.metrics_history['gpu_memory_total'][-1] if self.metrics_history['gpu_memory_total'] else 0
                }
                
                if self.metrics_history['gpu_temperature']:
                    temps = [t for t in self.metrics_history['gpu_temperature'] if t > 0]
                    if temps:
                        summary['gpu_stats']['avg_temperature'] = np.mean(temps)
                        summary['gpu_stats']['max_temperature'] = np.max(temps)
        
        # Training statistics
        if self.training_metrics['epoch_times']:
            summary['training_stats'] = {
                'total_epochs': len(self.training_metrics['epoch_times']),
                'avg_epoch_time': np.mean(self.training_metrics['epoch_times']),
                'total_training_time': np.sum(self.training_metrics['epoch_times'])
            }
            
            if self.training_metrics['batch_times']:
                summary['training_stats']['avg_batch_time'] = np.mean(self.training_metrics['batch_times'])
                summary['training_stats']['total_batches'] = len(self.training_metrics['batch_times'])
        
        return summary
    
    def print_performance_report(self):
        """Print a comprehensive performance report."""
        summary = self.get_performance_summary()
        
        print("\n" + "="*60)
        print("📊 PERFORMANCE MONITORING REPORT")
        print("="*60)
        
        if summary['monitoring_duration'] > 0:
            print(f"⏱️ Monitoring Duration: {summary['monitoring_duration']:.1f}s")
            
            # CPU Report
            if summary['cpu_stats']:
                cpu = summary['cpu_stats']
                print(f"\n💻 CPU Performance:")
                print(f"   • Average Utilization: {cpu['avg_utilization']:.1f}%")
                print(f"   • Peak Utilization: {cpu['max_utilization']:.1f}%")
            
            # Memory Report
            if summary['memory_stats']:
                mem = summary['memory_stats']
                print(f"\n🧠 Memory Usage:")
                print(f"   • Average Usage: {mem['avg_usage']:.1f}%")
                print(f"   • Peak Usage: {mem['max_usage']:.1f}%")
            
            # GPU Report
            if summary['gpu_stats']:
                gpu = summary['gpu_stats']
                print(f"\n🚀 GPU Performance:")
                print(f"   • Average Utilization: {gpu['avg_utilization']:.1f}%")
                print(f"   • Peak Utilization: {gpu['max_utilization']:.1f}%")
                print(f"   • Average Memory Used: {gpu['avg_memory_used']:.1f}GB")
                print(f"   • Peak Memory Used: {gpu['max_memory_used']:.1f}GB")
                print(f"   • Total GPU Memory: {gpu['memory_total']:.1f}GB")
                
                if 'avg_temperature' in gpu:
                    print(f"   • Average Temperature: {gpu['avg_temperature']:.1f}°C")
                    print(f"   • Peak Temperature: {gpu['max_temperature']:.1f}°C")
            
            # Training Report
            if summary['training_stats']:
                train = summary['training_stats']
                print(f"\n🎯 Training Performance:")
                print(f"   • Total Epochs: {train['total_epochs']}")
                print(f"   • Average Epoch Time: {train['avg_epoch_time']:.2f}s")
                print(f"   • Total Training Time: {train['total_training_time']:.1f}s")
                
                if 'avg_batch_time' in train:
                    print(f"   • Average Batch Time: {train['avg_batch_time']:.3f}s")
                    print(f"   • Total Batches: {train['total_batches']}")
        else:
            print("⚠️ No monitoring data available")
        
        print("="*60)
    
    def reset_metrics(self):
        """Reset all collected metrics."""
        for key in self.metrics_history:
            self.metrics_history[key].clear()
        
        for key in self.training_metrics:
            self.training_metrics[key].clear()
        
        # Clear queue
        while not self.metrics_queue.empty():
            try:
                self.metrics_queue.get_nowait()
            except queue.Empty:
                break
        
        print("🔄 Performance metrics reset")

# Global performance monitor instance
_global_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def start_performance_monitoring():
    """Start global performance monitoring."""
    monitor = get_performance_monitor()
    monitor.start_monitoring()

def stop_performance_monitoring():
    """Stop global performance monitoring."""
    monitor = get_performance_monitor()
    monitor.stop_monitoring()

def print_performance_report():
    """Print global performance report."""
    monitor = get_performance_monitor()
    monitor.print_performance_report()
