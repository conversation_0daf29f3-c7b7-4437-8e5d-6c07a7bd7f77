#!/usr/bin/env python3
"""
End-to-end test of SAITS model with the fixed data pipeline
"""

import numpy as np
import pandas as pd
import torch
from data_handler import create_sequences, introduce_missingness, normalize_data
from ml_core import MODEL_REGISTRY

def test_full_saits_pipeline():
    """Test the complete SAITS pipeline with realistic well log data"""
    print("🔍 Testing complete SAITS pipeline...")
    
    # Create realistic well log data
    n_rows_per_well = 300
    np.random.seed(42)
    
    df = pd.DataFrame({
        'WELL': ['Well_A'] * n_rows_per_well + ['Well_B'] * n_rows_per_well,
        'MD': np.concatenate([np.linspace(1000, 1600, n_rows_per_well), 
                             np.linspace(2000, 2600, n_rows_per_well)]),
        'GR': np.random.normal(50, 20, n_rows_per_well * 2),
        'NPHI': np.random.normal(0.2, 0.1, n_rows_per_well * 2),
        'RHOB': np.random.normal(2.3, 0.3, n_rows_per_well * 2),
        'P-WAVE': np.random.normal(200, 50, n_rows_per_well * 2)
    })
    
    # Add realistic missing sections
    for well in ['Well_A', 'Well_B']:
        well_mask = df['WELL'] == well
        well_indices = df[well_mask].index
        
        for i in range(3):
            start_idx = np.random.choice(well_indices[10:-10])
            end_idx = start_idx + np.random.randint(5, 10)
            df.loc[start_idx:end_idx, 'P-WAVE'] = np.nan
    
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'P-WAVE'
    all_features = feature_cols + [target_col]
    
    print(f"Data shape: {df.shape}")
    print(f"Target missing values: {df[target_col].isna().sum()}")
    
    try:
        # Split data
        wells = df['WELL'].unique()
        train_wells = [wells[0]]  # Use first well for training
        val_wells = [wells[1]]    # Use second well for validation
        
        train_df = df[df['WELL'].isin(train_wells)]
        val_df = df[df['WELL'].isin(val_wells)]
        
        print(f"Training wells: {train_wells}")
        print(f"Validation wells: {val_wells}")
        
        # Normalize data
        print("Normalizing data...")
        train_df_scaled, scalers = normalize_data(train_df, all_features, use_enhanced=True)
        val_df_scaled, _ = normalize_data(val_df, all_features, use_enhanced=True, scalers=scalers)
        
        # Create sequences
        print("Creating sequences...")
        sequence_len = 32
        train_sequences_true, train_metadata = create_sequences(
            train_df_scaled, 'WELL', all_features, 
            sequence_len=sequence_len, use_enhanced=True
        )
        
        if train_sequences_true.shape[0] == 0:
            print("❌ No training sequences created")
            return False
            
        # Introduce missingness
        print("Introducing missingness...")
        train_sequences_missing = introduce_missingness(
            train_sequences_true, missing_rate=0.3, use_enhanced=True,
            target_col_name=target_col, feature_names=all_features
        )
        
        # Convert to tensors
        print("Converting to tensors...")
        train_tensor = torch.from_numpy(train_sequences_missing.astype(np.float32))
        truth_tensor = torch.from_numpy(train_sequences_true.astype(np.float32))
        
        print(f"Train tensor shape: {train_tensor.shape}")
        print(f"Truth tensor shape: {truth_tensor.shape}")
        
        # Initialize SAITS model
        print("Initializing SAITS model...")
        saits_config = MODEL_REGISTRY['saits']
        hparams = {
            'n_features': len(all_features),
            'sequence_len': sequence_len,
            'epochs': 5,  # Small number for testing
            'batch_size': 16,
            'learning_rate': 1e-3,
            'n_layers': 2,
            'd_model': 128,  # Smaller for faster testing
            'n_heads': 4,
            'dropout': 0.1
        }
        
        model = saits_config['model_class'](**hparams)
        
        # Train model
        print("Training SAITS model...")
        model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'], batch_size=hparams['batch_size'])
        
        # Test prediction
        print("Testing prediction...")
        predictions = model.predict(train_tensor)
        
        print(f"✅ Predictions shape: {predictions.shape}")
        print(f"✅ Predictions type: {type(predictions)}")
        
        # Basic validation
        if predictions.shape == train_tensor.shape:
            print("✅ Prediction shape matches input shape")
        else:
            print(f"❌ Shape mismatch: {predictions.shape} vs {train_tensor.shape}")
            return False
            
        # Check for reasonable values (not all NaN)
        if not torch.isnan(predictions).all():
            print("✅ Predictions contain non-NaN values")
        else:
            print("❌ All predictions are NaN")
            return False
            
        print("✅ Full SAITS pipeline test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in SAITS pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_saits_pipeline()
    if success:
        print("\n✅ SAITS model is ready for use in the main application!")
    else:
        print("\n❌ SAITS model needs further debugging.")
