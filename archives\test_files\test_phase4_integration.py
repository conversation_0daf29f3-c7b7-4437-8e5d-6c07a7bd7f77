#!/usr/bin/env python3
"""
Comprehensive integration test suite for Phase 4 implementation.
Tests complete 5-model advanced deep learning suite and all new features.
"""

import unittest
import torch
import numpy as np
import warnings
from typing import Dict, Any, List
import tempfile
import os

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestPhase4Integration(unittest.TestCase):
    """Test suite for Phase 4 complete integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create synthetic test data
        np.random.seed(42)
        torch.manual_seed(42)
        
        self.n_samples = 50  # Smaller for faster testing
        self.sequence_len = 64
        self.n_features = 4
        
        # Generate synthetic well log data
        depth = np.linspace(0, 1000, self.sequence_len)
        
        # Create realistic well log patterns
        gr_base = 50 + 30 * np.sin(depth / 100) + np.random.normal(0, 5, self.sequence_len)
        nphi_base = 0.2 + 0.1 * np.cos(depth / 150) + np.random.normal(0, 0.02, self.sequence_len)
        rhob_base = 2.3 + 0.3 * np.sin(depth / 200) + np.random.normal(0, 0.05, self.sequence_len)
        dt_base = 100 + 50 * np.cos(depth / 120) + np.random.normal(0, 10, self.sequence_len)
        
        # Create multiple samples
        self.test_data = []
        self.truth_data = []
        
        for i in range(self.n_samples):
            variation = np.random.normal(1, 0.1, self.sequence_len)
            
            sample = np.column_stack([
                gr_base * variation,
                nphi_base * variation,
                rhob_base * variation,
                dt_base * variation
            ])
            
            truth_sample = sample.copy()
            
            # Create missing data
            missing_mask = np.random.random((self.sequence_len, self.n_features)) < 0.15
            sample[missing_mask] = np.nan
            
            self.test_data.append(sample)
            self.truth_data.append(truth_sample)
        
        self.test_data = np.array(self.test_data)
        self.truth_data = np.array(self.truth_data)
        
        # Convert to tensors
        self.test_tensor = torch.tensor(self.test_data, dtype=torch.float32)
        self.truth_tensor = torch.tensor(self.truth_data, dtype=torch.float32)
        
        print(f"🧪 Integration test data created: {self.test_tensor.shape}")
    
    def test_all_advanced_models_available(self):
        """Test that all 5 advanced models are available."""
        try:
            from models.advanced_models import ADVANCED_MODELS_STATUS
            
            expected_models = ['saits', 'brits', 'enhanced_unet', 'transformer', 'mrnn']
            
            for model_name in expected_models:
                self.assertTrue(
                    ADVANCED_MODELS_STATUS.get(model_name, False),
                    f"Model {model_name} not available"
                )
            
            available_count = sum(ADVANCED_MODELS_STATUS.values())
            self.assertEqual(available_count, 5, f"Expected 5 models, got {available_count}")
            
            print("✅ All 5 advanced models are available")
            
        except ImportError:
            self.skipTest("Advanced models module not available")
    
    def test_model_registry_integration(self):
        """Test that all models are properly integrated into the registry."""
        try:
            from ml_core import MODEL_REGISTRY
            from models.advanced_models import ADVANCED_MODELS_STATUS
            
            advanced_models = [k for k, v in ADVANCED_MODELS_STATUS.items() if v]
            
            for model_name in advanced_models:
                self.assertIn(model_name, MODEL_REGISTRY, f"Model {model_name} not in registry")
                
                config = MODEL_REGISTRY[model_name]
                self.assertEqual(config['type'], 'deep_advanced')
                self.assertIsNotNone(config['model_class'])
                self.assertIn('hyperparameters', config)
            
            print(f"✅ All {len(advanced_models)} models integrated into registry")
            
        except ImportError:
            self.skipTest("Model registry not available")
    
    def test_new_models_training_and_prediction(self):
        """Test training and prediction for new models (Transformer and mRNN)."""
        try:
            from models.advanced_models import TransformerModel, MRNNModel
            
            # Test Transformer
            transformer = TransformerModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                d_model=128,
                n_heads=4,
                n_encoder_layers=2,
                epochs=2,
                batch_size=16
            )
            
            transformer.fit(self.test_tensor[:20], self.truth_tensor[:20])
            transformer_predictions = transformer.predict(self.test_tensor[:5])
            
            self.assertTrue(transformer.is_fitted)
            self.assertEqual(transformer_predictions.shape, self.test_tensor[:5].shape)
            
            # Test mRNN
            mrnn = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[32, 64],
                n_layers=2,
                epochs=2,
                batch_size=16
            )
            
            mrnn.fit(self.test_tensor[:20], self.truth_tensor[:20])
            mrnn_predictions = mrnn.predict(self.test_tensor[:5])
            
            self.assertTrue(mrnn.is_fitted)
            self.assertEqual(mrnn_predictions.shape, self.test_tensor[:5].shape)
            
            print("✅ New models (Transformer and mRNN) training and prediction test passed")
            
        except ImportError:
            self.skipTest("New models not available")
    
    def test_advanced_visualization_features(self):
        """Test advanced visualization features."""
        try:
            from utils.visualization_advanced import AdvancedVisualization
            
            # Create mock results for testing
            mock_results = {
                'model1': {
                    'evaluations': [{
                        'predictions': np.random.randn(100),
                        'actuals': np.random.randn(100),
                        'mae': 0.5,
                        'rmse': 0.7,
                        'r2': 0.8,
                        'mape': 10.0
                    }]
                },
                'model2': {
                    'evaluations': [{
                        'predictions': np.random.randn(100),
                        'actuals': np.random.randn(100),
                        'mae': 0.6,
                        'rmse': 0.8,
                        'r2': 0.7,
                        'mape': 12.0
                    }]
                }
            }
            
            viz = AdvancedVisualization()
            
            # Test that visualization methods can be called without errors
            # (We won't actually display plots in tests)
            try:
                # This would normally create plots, but we'll just test instantiation
                self.assertIsNotNone(viz)
                print("✅ Advanced visualization features available")
            except Exception as e:
                self.fail(f"Advanced visualization failed: {e}")
                
        except ImportError:
            self.skipTest("Advanced visualization not available")
    
    def test_performance_optimization_features(self):
        """Test performance optimization features."""
        try:
            from utils.optimization import GPUAccelerator, MemoryOptimizer, PerformanceMonitor
            
            # Test GPU accelerator
            gpu_accel = GPUAccelerator()
            self.assertIsNotNone(gpu_accel.device)
            
            # Test memory optimizer
            mem_opt = MemoryOptimizer()
            memory_stats = mem_opt.monitor_memory_usage()
            self.assertIn('total_gb', memory_stats)
            
            # Test performance monitor
            perf_monitor = PerformanceMonitor()
            system_status = perf_monitor.get_system_status()
            self.assertIn('cpu_percent', system_status)
            self.assertIn('memory', system_status)
            
            print("✅ Performance optimization features available")
            
        except ImportError:
            self.skipTest("Performance optimization features not available")
    
    def test_hyperparameter_tuning_features(self):
        """Test hyperparameter tuning features."""
        try:
            from utils.hyperparameter_tuning import HyperparameterTuner
            
            tuner = HyperparameterTuner()
            self.assertIsNotNone(tuner)
            
            # Test parameter suggestion for different models
            import optuna
            study = optuna.create_study()
            trial = study.ask()
            
            transformer_params = tuner.suggest_hyperparameters(trial, 'transformer')
            self.assertIn('d_model', transformer_params)
            self.assertIn('n_heads', transformer_params)
            
            mrnn_params = tuner.suggest_hyperparameters(trial, 'mrnn')
            self.assertIn('hidden_sizes', mrnn_params)
            self.assertIn('n_layers', mrnn_params)
            
            print("✅ Hyperparameter tuning features available")
            
        except ImportError:
            self.skipTest("Hyperparameter tuning features not available")
    
    def test_gpu_acceleration_features(self):
        """Test GPU acceleration features."""
        try:
            from utils.gpu_acceleration import GPUManager, DataParallelManager
            
            # Test GPU manager
            gpu_manager = GPUManager()
            self.assertIsNotNone(gpu_manager.device)
            
            memory_stats = gpu_manager.get_memory_stats()
            self.assertIsInstance(memory_stats, dict)
            
            # Test data parallel manager
            dp_manager = DataParallelManager()
            self.assertIsNotNone(dp_manager)
            
            print("✅ GPU acceleration features available")
            
        except ImportError:
            self.skipTest("GPU acceleration features not available")
    
    def test_complete_workflow_with_new_models(self):
        """Test complete workflow with new models."""
        try:
            from ml_core import MODEL_REGISTRY
            from models.advanced_models import ADVANCED_MODELS_STATUS
            
            # Test with Transformer model
            if ADVANCED_MODELS_STATUS.get('transformer', False):
                model_config = MODEL_REGISTRY['transformer']
                model_class = model_config['model_class']
                
                # Create minimal hyperparameters for quick test
                hparams = {
                    'n_features': self.n_features,
                    'sequence_len': self.sequence_len,
                    'd_model': 128,
                    'n_heads': 4,
                    'n_encoder_layers': 2,
                    'epochs': 2,
                    'batch_size': 16
                }
                
                # Test complete workflow
                model = model_class(**hparams)
                model.fit(self.test_tensor[:20], self.truth_tensor[:20])
                predictions = model.predict(self.test_tensor[:5])
                
                self.assertIsInstance(predictions, torch.Tensor)
                self.assertEqual(predictions.shape, self.test_tensor[:5].shape)
            
            # Test with mRNN model
            if ADVANCED_MODELS_STATUS.get('mrnn', False):
                model_config = MODEL_REGISTRY['mrnn']
                model_class = model_config['model_class']
                
                hparams = {
                    'n_features': self.n_features,
                    'sequence_len': self.sequence_len,
                    'hidden_sizes': [32, 64],
                    'n_layers': 2,
                    'epochs': 2,
                    'batch_size': 16
                }
                
                model = model_class(**hparams)
                model.fit(self.test_tensor[:20], self.truth_tensor[:20])
                predictions = model.predict(self.test_tensor[:5])
                
                self.assertIsInstance(predictions, torch.Tensor)
                self.assertEqual(predictions.shape, self.test_tensor[:5].shape)
            
            print("✅ Complete workflow with new models test passed")
            
        except ImportError:
            self.skipTest("Workflow components not available")
    
    def test_backward_compatibility(self):
        """Test that existing functionality is preserved."""
        try:
            from ml_core import MODEL_REGISTRY
            
            # Test that existing models still work
            existing_models = ['xgboost', 'lightgbm', 'catboost']
            
            for model_name in existing_models:
                if model_name in MODEL_REGISTRY:
                    config = MODEL_REGISTRY[model_name]
                    self.assertIsNotNone(config['model_class'])
                    self.assertIn('hyperparameters', config)
            
            # Test that basic deep models still work if available
            basic_deep_models = ['autoencoder', 'unet']
            
            for model_name in basic_deep_models:
                if model_name in MODEL_REGISTRY:
                    config = MODEL_REGISTRY[model_name]
                    if config['model_class'] is not None:
                        self.assertIn('hyperparameters', config)
            
            print("✅ Backward compatibility test passed")
            
        except ImportError:
            self.skipTest("Model registry not available")
    
    def test_phase4_dependencies(self):
        """Test that all Phase 4 dependencies are available."""
        dependencies = [
            ('optuna', 'Hyperparameter optimization'),
            ('plotly', 'Interactive visualization'),
            ('psutil', 'System monitoring'),
            ('memory_profiler', 'Memory profiling')
        ]
        
        missing_deps = []
        
        for dep_name, description in dependencies:
            try:
                __import__(dep_name)
                print(f"✅ {description} ({dep_name}) available")
            except ImportError:
                missing_deps.append(f"{dep_name} ({description})")
        
        if missing_deps:
            self.skipTest(f"Missing dependencies: {', '.join(missing_deps)}")
        else:
            print("✅ All Phase 4 dependencies available")

def run_phase4_integration_tests():
    """Run all Phase 4 integration tests."""
    print("🧪 Running Phase 4 Integration Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPhase4Integration)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 All Phase 4 integration tests passed!")
        print("🚀 Phase 4 implementation is complete and functional!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        for test, traceback in result.failures + result.errors:
            print(f"\n❌ {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_phase4_integration_tests()
